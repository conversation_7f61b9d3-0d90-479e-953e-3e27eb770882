#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer la table principale personnel
"""

import mysql.connector
from mysql.connector import Error

def create_personnel_table():
    """Crée la table principale personnel"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("Connexion à la base de données réussie")
        
        # Désactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0;")
        
        # Supprimer la table si elle existe
        cursor.execute("DROP TABLE IF EXISTS personnel;")
        print("Table personnel supprimée si elle existait")
        
        # Créer la table personnel
        personnel_sql = """
        CREATE TABLE personnel (
            matricule VARCHAR(20) PRIMARY KEY,
            nom VARCHAR(80) NOT NULL,
            prenom VARCHAR(80) NOT NULL,
            nom_ar VARCHAR(80) NOT NULL,
            prenom_ar VARCHAR(80) NOT NULL,
            date_naissance DATE NOT NULL,
            lieu_naissance VARCHAR(100) NOT NULL,
            genre_id INT NOT NULL,
            categorie_id INT NOT NULL,
            groupe_sanguin_id INT NOT NULL,
            cin_numero VARCHAR(20) NOT NULL,
            cin_date_delivrance DATE NOT NULL,
            cin_date_expiration DATE NOT NULL,
            gsm VARCHAR(20) NOT NULL,
            telephone_domicile VARCHAR(20) NULL,
            taille_cm INT NOT NULL,
            lieu_residence VARCHAR(200) NOT NULL,
            service_id INT NOT NULL,
            specialite_id INT NULL,
            unite_id INT NOT NULL,
            grade_actuel_id INT NOT NULL,
            fonction VARCHAR(100) NOT NULL,
            date_prise_fonction DATE NOT NULL,
            ccp_numero VARCHAR(50) NOT NULL,
            compte_bancaire_numero VARCHAR(50) NULL,
            somme_numero VARCHAR(50) NOT NULL,
            date_engagement DATE NOT NULL,
            nom_pere VARCHAR(80) NOT NULL,
            prenom_pere VARCHAR(80) NOT NULL,
            nom_mere VARCHAR(80) NOT NULL,
            prenom_mere VARCHAR(80) NOT NULL,
            adresse_parents VARCHAR(200) NOT NULL,
            etat_matrimonial_id INT NOT NULL,
            nombre_enfants INT NULL,
            passeport_numero VARCHAR(20) NULL,
            passeport_date_delivrance DATE NULL,
            passeport_date_expiration DATE NULL,
            gsm_urgence VARCHAR(20) NOT NULL,
            lien_parente_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (genre_id) REFERENCES referentiel_genre(id_genre),
            FOREIGN KEY (categorie_id) REFERENCES referentiel_categorie(id_categorie),
            FOREIGN KEY (groupe_sanguin_id) REFERENCES referentiel_groupe_sanguin(id_groupe),
            FOREIGN KEY (service_id) REFERENCES referentiel_service(id_service),
            FOREIGN KEY (specialite_id) REFERENCES referentiel_specialite(id_specialite),
            FOREIGN KEY (unite_id) REFERENCES referentiel_unite(id_unite),
            FOREIGN KEY (grade_actuel_id) REFERENCES referentiel_grade(id_grade),
            FOREIGN KEY (etat_matrimonial_id) REFERENCES referentiel_etat_matrimonial(id_etat),
            FOREIGN KEY (lien_parente_id) REFERENCES referentiel_lien_parente(id_lien)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        
        cursor.execute(personnel_sql)
        print("✓ Table personnel créée avec succès")
        
        # Valider les changements
        connection.commit()
        
        # Réactiver les contraintes
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1;")
        
    except Error as e:
        print(f"Erreur : {e}")
        return False
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("Connexion fermée")
    
    return True

if __name__ == "__main__":
    print("=== CRÉATION TABLE PERSONNEL ===")
    print("Table principale du système RH")
    print("-" * 40)
    
    success = create_personnel_table()
    
    if success:
        print("\n🎉 Table personnel créée avec succès !")
    else:
        print("\n❌ Échec de la création")
