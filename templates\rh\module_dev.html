{% extends "rh/base_rh.html" %}

{% block title %}{{ titre }} - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="{{ icone }}"></i>
                                {{ titre }}
                            </h2>
                            <small class="text-muted">Division des Ressources Humaines - Inspection de l'Artillerie</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.dashboard') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour au Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu Principal -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="{{ icone }} fa-5x text-warning mb-3" style="opacity: 0.7;"></i>
                    </div>
                    
                    <h3 class="text-warning mb-3">Module en Cours de Développement</h3>
                    <p class="text-muted mb-4 fs-5">{{ description }}</p>
                    
                    <div class="row justify-content-center mb-4">
                        <div class="col-md-8">
                            <div class="alert alert-military">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Fonctionnalités Prévues :</strong>
                                <ul class="list-unstyled mt-2 mb-0">
                                    {% if titre == "Gestion des Absences" %}
                                    <li><i class="fas fa-check text-success me-2"></i>Gestion des permissions et congés</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Suivi des absences maladie</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Workflow d'approbation</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Calendrier des absences</li>
                                    {% elif titre == "Gestion des Formations" %}
                                    <li><i class="fas fa-check text-success me-2"></i>Catalogue des formations</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Planification et inscription</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Évaluation et certification</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Suivi des compétences</li>
                                    {% elif titre == "Gestion des Mutations" %}
                                    <li><i class="fas fa-check text-success me-2"></i>Demandes de mutation</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Gestion des affectations</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Workflow d'approbation</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Historique des mouvements</li>
                                    {% elif titre == "Suivi Médical et Santé" %}
                                    <li><i class="fas fa-check text-success me-2"></i>Visites médicales périodiques</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Suivi des aptitudes physiques</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Gestion des handicaps</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Alertes médicales</li>
                                    {% elif titre == "Gestion des Courriers RH" %}
                                    <li><i class="fas fa-check text-success me-2"></i>Courriers arrivée et départ</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Suivi et traçabilité</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Pièces jointes</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Notifications automatiques</li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions Rapides -->
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('rh.dashboard') }}" class="btn btn-military btn-lg">
                                    <i class="fas fa-tachometer-alt"></i> Retour au Tableau de Bord
                                </a>
                                <a href="{{ url_for('rh.liste_personnel') }}" class="btn btn-info-military btn-lg">
                                    <i class="fas fa-users"></i> Gestion du Personnel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Rapides -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-clock stat-icon"></i>
                <div class="stat-number">Q1</div>
                <div class="stat-label">Livraison Prévue</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-code stat-icon"></i>
                <div class="stat-number">100%</div>
                <div class="stat-label">Architecture Prête</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-database stat-icon"></i>
                <div class="stat-number">25+</div>
                <div class="stat-label">Tables Créées</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-shield-alt stat-icon"></i>
                <div class="stat-number">FAR</div>
                <div class="stat-label">Standard Militaire</div>
            </div>
        </div>
    </div>

    <!-- Roadmap -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-road"></i>
                        Feuille de Route de Développement
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="badge badge-success-military mb-2">TERMINÉ</div>
                                <h6>Phase 1</h6>
                                <small class="text-muted">Architecture & Design</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="badge badge-success-military mb-2">TERMINÉ</div>
                                <h6>Phase 2</h6>
                                <small class="text-muted">Base de Données</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="badge badge-warning-military mb-2">EN COURS</div>
                                <h6>Phase 3</h6>
                                <small class="text-muted">Modules Fonctionnels</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="badge badge-military mb-2">PLANIFIÉ</div>
                                <h6>Phase 4</h6>
                                <small class="text-muted">IA & Analytics</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-card {
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.badge-military {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--accent-color);
    font-weight: 700;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}
</style>
{% endblock %}
