{% extends "rh/base_rh.html" %}

{% block title %}Nouvelle Mutation - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-exchange-alt"></i>
                                Nouvelle Demande de Mutation
                            </h2>
                            <small class="text-muted">Création d'une nouvelle demande de mutation ou affectation</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.liste_mutations') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à la Liste
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Création -->
    <form method="POST" class="needs-validation" novalidate>
        <div class="row">
            <!-- Section 1: Informations de Base -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations de Base
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Militaire Concerné *</label>
                            <select name="militaire_id" class="form-control form-control-military" required>
                                <option value="">Sélectionner un militaire...</option>
                                {% for militaire in personnel %}
                                <option value="{{ militaire.id }}">
                                    {{ militaire.grade_actuel or '' }} {{ militaire.nom }} {{ militaire.prenom }} - {{ militaire.unite_affectation or 'N/A' }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">Veuillez sélectionner un militaire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Type de Mutation *</label>
                            <select name="type_mutation" class="form-control form-control-military" required>
                                <option value="">Sélectionner un type...</option>
                                {% for type_mut in types_mutations %}
                                <option value="{{ type_mut }}">{{ type_mut }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">Veuillez sélectionner un type de mutation</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date de Demande *</label>
                                <input type="date" name="date_demande" class="form-control form-control-military" 
                                       value="{{ date.today() }}" required>
                                <div class="invalid-feedback">La date de demande est obligatoire</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date Prévue</label>
                                <input type="date" name="date_prevue" class="form-control form-control-military">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 2: Unités et Postes -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-building"></i>
                            Unités et Postes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Unité d'Origine</label>
                            <select name="unite_origine" class="form-control form-control-military">
                                <option value="">Sélectionner l'unité d'origine...</option>
                                {% for unite in unites %}
                                <option value="{{ unite }}">{{ unite }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Poste d'Origine</label>
                            <input type="text" name="poste_origine" class="form-control form-control-military"
                                   placeholder="Ex: Chef de Section">
                        </div>
                        
                        <div class="text-center mb-3">
                            <i class="fas fa-arrow-down fa-2x text-warning"></i>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Unité de Destination *</label>
                            <select name="unite_destination" class="form-control form-control-military" required>
                                <option value="">Sélectionner l'unité de destination...</option>
                                {% for unite in unites %}
                                <option value="{{ unite }}">{{ unite }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">L'unité de destination est obligatoire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Poste de Destination</label>
                            <input type="text" name="poste_destination" class="form-control form-control-military"
                                   placeholder="Ex: Responsable Logistique">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Section 3: Motif et Observations -->
            <div class="col-lg-8 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt"></i>
                            Motif et Observations
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Motif de la Mutation</label>
                            <textarea name="motif" class="form-control form-control-military" rows="4" 
                                      placeholder="Décrivez le motif de la mutation (besoins du service, demande personnelle, etc.)"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Observations</label>
                            <textarea name="observations" class="form-control form-control-military" rows="3" 
                                      placeholder="Observations complémentaires, recommandations, etc."></textarea>
                        </div>
                        
                        <div class="alert alert-military">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information :</strong> Cette demande sera soumise pour approbation hiérarchique.
                            La mutation ne sera effective qu'après validation par l'autorité compétente.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 4: Validation -->
            <div class="col-lg-4 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-check"></i>
                            Validation
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning-military mb-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Attention :</strong>
                            <ul class="mb-0 mt-2">
                                <li>Vérifiez les unités sélectionnées</li>
                                <li>Assurez-vous que le motif est clair</li>
                                <li>La demande sera en attente d'approbation</li>
                                <li>Notification automatique des parties concernées</li>
                            </ul>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success-military btn-lg">
                                <i class="fas fa-paper-plane"></i> Soumettre la Demande
                            </button>
                            <a href="{{ url_for('rh.liste_mutations') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                        </div>
                        
                        <hr class="my-3">
                        
                        <div class="text-center">
                            <h6 class="text-warning mb-2">Actions Rapides</h6>
                            <div class="d-grid gap-1">
                                <a href="{{ url_for('rh.dashboard') }}" class="btn btn-military btn-sm">
                                    <i class="fas fa-tachometer-alt"></i> Tableau de Bord
                                </a>
                                <a href="{{ url_for('rh.liste_mutations') }}" class="btn btn-info-military btn-sm">
                                    <i class="fas fa-exchange-alt"></i> Liste Mutations
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control-military:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 213, 79, 0.25);
}

.invalid-feedback {
    color: var(--danger-color);
    font-weight: 600;
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-control:valid {
    border-color: var(--success-color);
}

.alert-warning-military {
    border-left: 4px solid var(--warning-color);
    background: rgba(255, 152, 0, 0.1);
    border-radius: 8px;
}

.alert-warning-military ul {
    padding-left: 1.2rem;
}

.card-military {
    transition: all 0.3s ease;
}

.card-military:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 213, 79, 0.15);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Validation en temps réel
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
    
})();

// Auto-remplissage de l'unité d'origine selon le militaire sélectionné
document.querySelector('select[name="militaire_id"]').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const uniteOrigineSelect = document.querySelector('select[name="unite_origine"]');
    
    if (selectedOption.value) {
        const text = selectedOption.text;
        const uniteMatch = text.match(/- (.+)$/);
        
        if (uniteMatch) {
            const unite = uniteMatch[1];
            // Chercher l'option correspondante dans le select des unités d'origine
            for (let option of uniteOrigineSelect.options) {
                if (option.value === unite) {
                    uniteOrigineSelect.value = unite;
                    break;
                }
            }
        }
    }
});

// Suggestions de motifs selon le type de mutation
document.querySelector('select[name="type_mutation"]').addEventListener('change', function() {
    const motifTextarea = document.querySelector('textarea[name="motif"]');
    const suggestions = {
        'Affectation': 'Affectation pour besoins du service selon les nécessités opérationnelles.',
        'Mutation': 'Mutation demandée pour raisons personnelles ou professionnelles.',
        'Détachement': 'Détachement temporaire pour mission spécifique ou formation.',
        'Permutation': 'Permutation avec un autre militaire pour optimisation des ressources.'
    };
    
    if (suggestions[this.value] && !motifTextarea.value) {
        motifTextarea.value = suggestions[this.value];
    }
});

// Validation des dates
document.querySelector('input[name="date_demande"]').addEventListener('change', function() {
    const datePrevueInput = document.querySelector('input[name="date_prevue"]');
    datePrevueInput.min = this.value;
    
    if (datePrevueInput.value && datePrevueInput.value < this.value) {
        datePrevueInput.value = this.value;
    }
});

// Animation des cartes
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card-military');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    if (this.checkValidity()) {
        const confirmation = confirm('Êtes-vous sûr de vouloir soumettre cette demande de mutation ?');
        if (!confirmation) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
