#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ÉTAPE 1: Supprimer toutes les tables liées à la division RH
"""

import mysql.connector
from mysql.connector import Error

def delete_rh_tables():
    """Supprimer toutes les tables RH existantes"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='gestion_vehicules',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🗑️ ÉTAPE 1: SUPPRESSION DES TABLES RH")
        print("=" * 60)
        
        # Désactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # Liste de toutes les tables RH à supprimer
        tables_rh = [
            # Tables de référence
            'referentiel_genre',
            'referentiel_groupe_sanguin', 
            'referentiel_categorie',
            'referentiel_service',
            'referentiel_specialite',
            'referentiel_unite',
            'referentiel_grade',
            'referentiel_etat_matrimonial',
            'referentiel_langue',
            'referentiel_lien_parente',
            'referentiel_type_absence',
            
            # Tables principales
            'personnel',
            'personnel_langue',
            'historique_grade',
            
            # Tables famille
            'conjoint',
            'enfant',
            
            # Tables médicales
            'situation_medicale',
            'vaccination',
            'ptc',
            'hospitalisation',
            
            # Tables absences/mouvements
            'permission',
            'desertion',
            'detachement',
            'mutation_inter_bie',
            'sejour_ops',
            'sejour_operationnel',
            'liberation',
            'sanction',
            
            # Tables anciennes (si elles existent)
            'absence_desertion',
            'absence_detachement',
            'absence_permission',
            'mouvement_interbie'
        ]
        
        # Supprimer chaque table
        tables_supprimees = 0
        for table in tables_rh:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"✅ Table '{table}' supprimée")
                tables_supprimees += 1
            except Error as e:
                print(f"⚠️ Erreur lors de la suppression de '{table}': {e}")
        
        # Réactiver les contraintes
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print(f"\n🎉 ÉTAPE 1 TERMINÉE !")
        print(f"✅ {tables_supprimees} tables RH supprimées")
        
        return True
        
    except Error as e:
        print(f"❌ Erreur lors de la suppression : {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 ÉTAPE 1: SUPPRESSION DES TABLES RH")
    print("Préparation pour la reconstruction complète")
    print()
    
    if delete_rh_tables():
        print("\n✅ Étape 1 réussie !")
        print("🎯 Prêt pour l'étape 2 (nettoyage du code)")
    else:
        print("\n❌ Échec de l'étape 1")

if __name__ == "__main__":
    main()
