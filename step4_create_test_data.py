#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Étape 4 - Partie 2: <PERSON>réer des données de test pour le personnel militaire
Génération de 100 militaires avec des données réalistes
"""

from app import app
from db import db
from gestion_vehicules.rh.models import *
from datetime import datetime, date, timedelta
import random

def generate_test_personnel():
    """Génère 100 militaires de test avec des données réalistes"""
    
    with app.app_context():
        try:
            print("🎯 ÉTAPE 4 - PARTIE 2: CRÉATION DES DONNÉES DE TEST")
            print("=" * 60)
            
            # Récupérer les données de référence
            genres = ReferentielGenre.query.all()
            groupes_sanguins = ReferentielGroupeSanguin.query.all()
            categories = ReferentielCategorie.query.all()
            services = ReferentielService.query.all()
            specialites = ReferentielSpecialite.query.all()
            unites = ReferentielUnite.query.all()
            grades = ReferentielGrade.query.all()
            etats_matrimoniaux = ReferentielEtatMatrimonial.query.all()
            langues = ReferentielLangue.query.all()
            liens_parente = ReferentielLienParente.query.all()
            
            print(f"📊 Données de référence chargées:")
            print(f"   • {len(genres)} genres")
            print(f"   • {len(groupes_sanguins)} groupes sanguins")
            print(f"   • {len(categories)} catégories")
            print(f"   • {len(services)} services")
            print(f"   • {len(specialites)} spécialités")
            print(f"   • {len(unites)} unités")
            print(f"   • {len(grades)} grades")
            print(f"   • {len(etats_matrimoniaux)} états matrimoniaux")
            print(f"   • {len(langues)} langues")
            print(f"   • {len(liens_parente)} liens de parenté")
            
            # Listes de noms et prénoms marocains
            prenoms_masculins = [
                'Ahmed', 'Mohammed', 'Hassan', 'Omar', 'Youssef', 'Khalid', 'Abdelaziz', 'Rachid',
                'Said', 'Mustapha', 'Abdellah', 'Karim', 'Noureddine', 'Driss', 'Brahim',
                'Abderrahim', 'Hamid', 'Larbi', 'Abdelkader', 'Jamal', 'Fouad', 'Tarik',
                'Amine', 'Hicham', 'Redouane', 'Aziz', 'Mehdi', 'Samir', 'Othmane', 'Zakaria'
            ]
            
            prenoms_feminins = [
                'Fatima', 'Aicha', 'Khadija', 'Zineb', 'Amina', 'Salma', 'Nadia', 'Laila',
                'Samira', 'Rajae', 'Houda', 'Siham', 'Karima', 'Malika', 'Souad',
                'Latifa', 'Naima', 'Hafida', 'Zahra', 'Meryem', 'Hanane', 'Imane',
                'Ghizlane', 'Sanae', 'Widad', 'Btissam', 'Nezha', 'Fadila', 'Yamina', 'Zohra'
            ]
            
            noms_famille = [
                'Alami', 'Benali', 'Cherkaoui', 'Douiri', 'El Fassi', 'Filali', 'Ghazi', 'Hajji',
                'Idrissi', 'Jebari', 'Kettani', 'Lahlou', 'Mansouri', 'Naciri', 'Ouali',
                'Qadiri', 'Radi', 'Sabri', 'Tazi', 'Usmani', 'Wahbi', 'Yaacoubi', 'Ziani',
                'Berrada', 'Chraibi', 'Drissi', 'El Amrani', 'Fassi', 'Guerraoui', 'Hakim',
                'Ismaili', 'Jaidi', 'Kabbaj', 'Lamrani', 'Mekouar', 'Nejjar', 'Ouazzani'
            ]
            
            villes_maroc = [
                'Rabat', 'Casablanca', 'Fès', 'Marrakech', 'Agadir', 'Tanger', 'Meknès', 'Oujda',
                'Kenitra', 'Tétouan', 'Safi', 'Mohammedia', 'Khouribga', 'Beni Mellal', 'El Jadida',
                'Nador', 'Taza', 'Settat', 'Berrechid', 'Khemisset', 'Inezgane', 'Ksar El Kebir',
                'Larache', 'Guelmim', 'Berkane', 'Taourirt', 'Bouskoura', 'Fquih Ben Salah'
            ]
            
            print("\n👥 Génération de 100 militaires de test...")
            
            # Supprimer le personnel existant
            Personnel.query.delete()
            db.session.commit()
            
            for i in range(1, 101):
                # Générer un genre aléatoire
                genre = random.choice(genres)
                is_male = genre.libelle == 'Masculin'
                
                # Choisir un prénom selon le genre
                prenom = random.choice(prenoms_masculins if is_male else prenoms_feminins)
                nom = random.choice(noms_famille)
                
                # Générer un matricule unique
                matricule = f"M{i:06d}"
                
                # Générer des dates
                date_naissance = date(
                    random.randint(1970, 2000),
                    random.randint(1, 12),
                    random.randint(1, 28)
                )
                
                date_engagement = date(
                    random.randint(2000, 2023),
                    random.randint(1, 12),
                    random.randint(1, 28)
                )
                
                date_prise_fonction = date_engagement + timedelta(days=random.randint(0, 365))
                
                # Générer CIN
                cin_numero = f"A{random.randint(100000, 999999)}"
                cin_date_delivrance = date_naissance + timedelta(days=18*365 + random.randint(0, 365))
                cin_date_expiration = cin_date_delivrance + timedelta(days=10*365)
                
                # Choisir des références aléatoires
                categorie = random.choice(categories)
                service = random.choice(services)

                # Spécialité liée au service (ou None si aucune)
                specialites_service = [s for s in specialites if s.service_id == service.id_service]
                specialite = random.choice(specialites_service) if specialites_service else None

                unite = random.choice(unites)
                grade = random.choice(grades)
                groupe_sanguin = random.choice(groupes_sanguins)
                etat_matrimonial = random.choice(etats_matrimoniaux)
                lien_parente = random.choice(liens_parente)
                
                # Générer des informations personnelles
                lieu_naissance = random.choice(villes_maroc)
                lieu_residence = f"{random.randint(1, 999)} Rue {random.choice(['Hassan II', 'Mohammed V', 'Al Massira', 'Al Andalous'])}, {random.choice(villes_maroc)}"
                
                # Créer le personnel
                personnel = Personnel(
                    matricule=matricule,
                    nom=nom,
                    prenom=prenom,
                    nom_ar=f"{nom} (عربي)",
                    prenom_ar=f"{prenom} (عربي)",
                    date_naissance=date_naissance,
                    lieu_naissance=lieu_naissance,
                    genre_id=genre.id_genre,
                    categorie_id=categorie.id_categorie,
                    groupe_sanguin_id=groupe_sanguin.id_groupe,
                    cin_numero=cin_numero,
                    cin_date_delivrance=cin_date_delivrance,
                    cin_date_expiration=cin_date_expiration,
                    gsm=f"06{random.randint(10000000, 99999999)}",
                    telephone_domicile=f"05{random.randint(10000000, 99999999)}" if random.choice([True, False]) else None,
                    taille_cm=random.randint(160, 190),
                    lieu_residence=lieu_residence,
                    service_id=service.id_service,
                    specialite_id=specialite.id_specialite if specialite else None,
                    unite_id=unite.id_unite,
                    grade_actuel_id=grade.id_grade,
                    fonction=f"Fonction {random.choice(['Opérationnelle', 'Administrative', 'Technique', 'Logistique'])}",
                    date_prise_fonction=date_prise_fonction,
                    ccp_numero=f"CCP{random.randint(100000, 999999)}",
                    compte_bancaire_numero=f"BA{random.randint(1000000000, 9999999999)}" if random.choice([True, False]) else None,
                    somme_numero=f"SOMME{random.randint(100000, 999999)}",
                    date_engagement=date_engagement,
                    nom_pere=f"{random.choice(prenoms_masculins)} {nom}",
                    prenom_pere=random.choice(prenoms_masculins),
                    nom_mere=f"{random.choice(prenoms_feminins)} {random.choice(noms_famille)}",
                    prenom_mere=random.choice(prenoms_feminins),
                    adresse_parents=f"{random.choice(villes_maroc)}, Maroc",
                    etat_matrimonial_id=etat_matrimonial.id_etat,
                    nombre_enfants=random.randint(0, 4) if etat_matrimonial.libelle == 'Marié(e)' else None,
                    passeport_numero=f"P{random.randint(1000000, 9999999)}" if random.choice([True, False]) else None,
                    passeport_date_delivrance=cin_date_delivrance if random.choice([True, False]) else None,
                    passeport_date_expiration=cin_date_expiration if random.choice([True, False]) else None,
                    gsm_urgence=f"06{random.randint(10000000, 99999999)}",
                    lien_parente_id=lien_parente.id_lien
                )
                
                db.session.add(personnel)
                
                # Afficher le progrès
                if i % 10 == 0:
                    print(f"   ✓ {i}/100 militaires créés...")
            
            # Valider tous les ajouts
            db.session.commit()
            print(f"   ✓ 100/100 militaires créés avec succès !")
            
            # Ajouter quelques langues aux militaires
            print("\n🗣️ Attribution des langues aux militaires...")
            personnel_list = Personnel.query.all()
            for p in personnel_list[:50]:  # Attribuer des langues à 50 militaires
                # Tous parlent arabe et français
                langue_ar = ReferentielLangue.query.filter_by(code_iso='AR').first()
                langue_fr = ReferentielLangue.query.filter_by(code_iso='FR').first()
                
                if langue_ar:
                    pl_ar = PersonnelLangue(personnel_matricule=p.matricule, langue_id=langue_ar.id_langue)
                    db.session.add(pl_ar)
                
                if langue_fr:
                    pl_fr = PersonnelLangue(personnel_matricule=p.matricule, langue_id=langue_fr.id_langue)
                    db.session.add(pl_fr)
                
                # Certains parlent aussi anglais
                if random.choice([True, False]):
                    langue_en = ReferentielLangue.query.filter_by(code_iso='EN').first()
                    if langue_en:
                        pl_en = PersonnelLangue(personnel_matricule=p.matricule, langue_id=langue_en.id_langue)
                        db.session.add(pl_en)
            
            db.session.commit()
            print("   ✓ Langues attribuées aux militaires")
            
            # Ajouter quelques historiques de grades
            print("\n🎖️ Création d'historiques de grades...")
            for p in personnel_list[:30]:  # Historique pour 30 militaires
                # Grade précédent (niveau inférieur)
                grades_inferieurs = [g for g in grades if g.niveau < p.grade_actuel.niveau]
                if grades_inferieurs:
                    grade_precedent = random.choice(grades_inferieurs)
                    date_effet = p.date_engagement + timedelta(days=random.randint(0, 365))
                    
                    historique = HistoriqueGrade(
                        personnel_matricule=p.matricule,
                        grade_id=grade_precedent.id_grade,
                        date_effet=date_effet
                    )
                    db.session.add(historique)
            
            db.session.commit()
            print("   ✓ Historiques de grades créés")
            
            print("\n✅ PARTIE 2 TERMINÉE AVEC SUCCÈS !")
            print("100 militaires de test créés avec des données réalistes.")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la génération: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    success = generate_test_personnel()
    
    if success:
        print("\n🎉 Données de test créées avec succès !")
        print("Le système RH est maintenant prêt à être testé.")
    else:
        print("\n❌ Échec de la génération des données de test")
