{% extends "rh/base_rh.html" %}

{% block title %}Gestion des Formations - RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-graduation-cap"></i>
                                Gestion des Formations et Compétences
                            </h2>
                            <small class="text-muted">Suivi des formations, certifications et développement des compétences</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouvelle_formation') }}" class="btn btn-success-military">
                                <i class="fas fa-plus"></i> Nouvelle Formation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Filtres et Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label-military">Recherche</label>
                            <input type="text" name="search" class="form-control form-control-military" 
                                   placeholder="Nom, formation, organisme..." value="{{ search }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label-military">Résultat</label>
                            <select name="resultat" class="form-control form-control-military">
                                <option value="">Tous les résultats</option>
                                {% for resultat in resultats %}
                                <option value="{{ resultat }}" {% if resultat == resultat_filter %}selected{% endif %}>
                                    {{ resultat }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label-military">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-military">
                                    <i class="fas fa-search"></i> Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-graduation-cap stat-icon"></i>
                <div class="stat-number">{{ formations.total }}</div>
                <div class="stat-label">Total Formations</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-clock stat-icon"></i>
                <div class="stat-number">{{ formations.items|selectattr('resultat', 'equalto', 'En cours')|list|length }}</div>
                <div class="stat-label">En Cours</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-check stat-icon"></i>
                <div class="stat-number">{{ formations.items|selectattr('resultat', 'equalto', 'Réussi')|list|length }}</div>
                <div class="stat-label">Réussies</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-certificate stat-icon"></i>
                <div class="stat-number">{{ formations.items|selectattr('certificat_obtenu', 'equalto', true)|list|length }}</div>
                <div class="stat-label">Certifiées</div>
            </div>
        </div>
    </div>

    <!-- Liste des Formations -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                Liste des Formations
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group">
                                <button class="btn btn-info-military btn-sm" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button class="btn btn-info-military btn-sm" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                                <button class="btn btn-warning-military btn-sm" onclick="printList()">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if formations.items %}
                    <div class="table-responsive">
                        <table class="table table-military mb-0">
                            <thead>
                                <tr>
                                    <th>Militaire</th>
                                    <th>Formation</th>
                                    <th>Organisme</th>
                                    <th>Période</th>
                                    <th>Durée</th>
                                    <th>Résultat</th>
                                    <th>Note</th>
                                    <th>Certificat</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for formation in formations.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ formation.militaire.nom }} {{ formation.militaire.prenom }}</strong>
                                            <br><small class="text-muted">{{ formation.militaire.grade_actuel or 'N/A' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ formation.nom_formation }}</strong>
                                            {% if formation.lieu_formation %}
                                            <br><small class="text-muted"><i class="fas fa-map-marker-alt"></i> {{ formation.lieu_formation }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>{{ formation.organisme_formateur or 'N/A' }}</td>
                                    <td>
                                        {% if formation.date_debut %}
                                        <div>
                                            <strong>{{ formation.date_debut.strftime('%d/%m/%Y') }}</strong>
                                            {% if formation.date_fin %}
                                            <br><small class="text-muted">au {{ formation.date_fin.strftime('%d/%m/%Y') }}</small>
                                            {% endif %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">Non définie</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if formation.duree_heures %}
                                        <span class="badge badge-info-military">{{ formation.duree_heures }}h</span>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if formation.resultat == 'Réussi' %}
                                        <span class="badge badge-success-military">
                                            <i class="fas fa-check"></i> {{ formation.resultat }}
                                        </span>
                                        {% elif formation.resultat == 'Échoué' %}
                                        <span class="badge badge-danger-military">
                                            <i class="fas fa-times"></i> {{ formation.resultat }}
                                        </span>
                                        {% elif formation.resultat == 'En cours' %}
                                        <span class="badge badge-warning-military">
                                            <i class="fas fa-clock"></i> {{ formation.resultat }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-military">{{ formation.resultat or 'N/A' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if formation.note_obtenue %}
                                        <span class="badge badge-info-military">{{ formation.note_obtenue }}/20</span>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if formation.certificat_obtenu %}
                                        <span class="badge badge-success-military">
                                            <i class="fas fa-certificate"></i> Oui
                                        </span>
                                        {% else %}
                                        <span class="badge badge-secondary">
                                            <i class="fas fa-times"></i> Non
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-info-military btn-sm" 
                                                    onclick="voirFormation({{ formation.id }})" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-warning-military btn-sm" 
                                                    onclick="modifierFormation({{ formation.id }})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if formation.resultat == 'En cours' %}
                                            <button class="btn btn-success-military btn-sm" 
                                                    onclick="terminerFormation({{ formation.id }})" title="Terminer">
                                                <i class="fas fa-flag-checkered"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune formation trouvée</h5>
                        <p class="text-muted">Aucune formation ne correspond aux critères de recherche.</p>
                        <a href="{{ url_for('rh.nouvelle_formation') }}" class="btn btn-success-military">
                            <i class="fas fa-plus"></i> Ajouter une Formation
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Pagination -->
                {% if formations.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination justify-content-center mb-0">
                            {% if formations.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_formations', page=formations.prev_num, search=search, resultat=resultat_filter) }}">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in formations.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != formations.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('rh.liste_formations', page=page_num, search=search, resultat=resultat_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if formations.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_formations', page=formations.next_num, search=search, resultat=resultat_filter) }}">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            Affichage de {{ formations.per_page * (formations.page - 1) + 1 }} à 
                            {{ formations.per_page * (formations.page - 1) + formations.items|length }} 
                            sur {{ formations.total }} résultats
                        </small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les détails -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: var(--card-bg); border: 1px solid var(--border-color);">
            <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                <h5 class="modal-title text-warning">
                    <i class="fas fa-graduation-cap"></i> Détails de la Formation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailsContent">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function voirFormation(id) {
    // Charger les détails de la formation
    fetch(`/rh/formations/${id}/details`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('detailsContent').innerHTML = data.html;
            const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des détails');
        });
}

function modifierFormation(id) {
    window.location.href = `/rh/formations/${id}/modifier`;
}

function terminerFormation(id) {
    if (confirm('Marquer cette formation comme terminée ?')) {
        fetch(`/rh/formations/${id}/terminer`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la mise à jour');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la mise à jour');
        });
    }
}

function exportToExcel() {
    window.location.href = '/rh/formations/export/excel?' + new URLSearchParams(window.location.search);
}

function exportToPDF() {
    window.location.href = '/rh/formations/export/pdf?' + new URLSearchParams(window.location.search);
}

function printList() {
    window.print();
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
