#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Correction des tables référentielles
"""

import mysql.connector
from mysql.connector import Error

def fix_referential_tables():
    """Corrige et peuple les tables référentielles"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("🔧 Correction des tables référentielles...")
        
        # Peupler les tables avec des données de base (sans colonnes optionnelles)
        print("\n📊 Peuplement des tables référentielles...")
        
        # Catégories
        categories = [
            ('Officier',),
            ('Sous-Officier',),
            ('Militaire du rang',)
        ]
        
        for (libelle,) in categories:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_categorie (libelle)
                VALUES (%s)
            """, (libelle,))
        print("✓ Catégories ajoutées")
        
        # Groupes sanguins
        groupes_sanguins = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
        for groupe in groupes_sanguins:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_groupe_sanguin (libelle)
                VALUES (%s)
            """, (groupe,))
        print("✓ Groupes sanguins ajoutés")
        
        # Services
        services = [
            'Artillerie',
            'Infanterie', 
            'Génie',
            'Transmissions',
            'Logistique',
            'Administration',
            'Santé'
        ]
        
        for service in services:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_service (libelle)
                VALUES (%s)
            """, (service,))
        print("✓ Services ajoutés")
        
        # Spécialités
        specialites = [
            'Canonnier',
            'Mécanicien',
            'Conducteur',
            'Secrétaire',
            'Cuisinier',
            'Infirmier',
            'Électricien'
        ]
        
        for specialite in specialites:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_specialite (libelle)
                VALUES (%s)
            """, (specialite,))
        print("✓ Spécialités ajoutées")
        
        # États matrimoniaux
        etats = ['Célibataire', 'Marié(e)', 'Divorcé(e)', 'Veuf(ve)']
        for etat in etats:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_etat_matrimonial (libelle)
                VALUES (%s)
            """, (etat,))
        print("✓ États matrimoniaux ajoutés")
        
        # Liens de parenté
        liens = ['Père', 'Mère', 'Frère', 'Sœur', 'Époux/Épouse', 'Fils', 'Fille', 'Autre']
        for lien in liens:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_lien_parente (libelle)
                VALUES (%s)
            """, (lien,))
        print("✓ Liens de parenté ajoutés")
        
        # Vérifier les données
        print("\n📊 Vérification des données :")
        
        tables_check = [
            ('referentiel_categorie', 'Catégories'),
            ('referentiel_groupe_sanguin', 'Groupes sanguins'),
            ('referentiel_service', 'Services'),
            ('referentiel_specialite', 'Spécialités'),
            ('referentiel_etat_matrimonial', 'États matrimoniaux'),
            ('referentiel_lien_parente', 'Liens de parenté')
        ]
        
        for table_name, description in tables_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   • {description}: {count} enregistrements")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n✅ Tables référentielles corrigées et peuplées avec succès !")
        return True
        
    except Error as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    print("🎯 CORRECTION DES TABLES RÉFÉRENTIELLES")
    print("=" * 50)
    
    success = fix_referential_tables()
    
    if success:
        print("\n🎉 Tables référentielles corrigées avec succès !")
        print("L'erreur ReferentielCategorie devrait être corrigée.")
    else:
        print("\n❌ Échec de la correction des tables")
