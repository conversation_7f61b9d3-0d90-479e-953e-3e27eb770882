"""
Script de création de la base de données RH militaire
Création des 25 tables et initialisation des données de référence
Division des Ressources Humaines - Inspection de l'Artillerie
Forces Armées Royales
"""

from app import app
from db import db
from gestion_vehicules.rh.models import *

def create_tables():
    """Crée toutes les tables RH"""
    try:
        with app.app_context():
            print("🔄 Création des tables RH...")
            
            # Créer toutes les tables
            db.create_all()
            
            print("✅ Tables RH créées avec succès!")
            return True
    
    except Exception as e:
        print(f"❌ Erreur lors de la création des tables: {str(e)}")
        return False

def init_referentiels():
    """Initialise les données de référence"""
    try:
        with app.app_context():
            print("🔄 Initialisation des données de référence...")
            
            # 1. Genres
            genres = ['Masculin', 'Féminin']
            for genre in genres:
                if not ReferentielGenre.query.filter_by(libelle=genre).first():
                    db.session.add(ReferentielGenre(libelle=genre))
            
            # 2. Groupes sanguins
            groupes_sanguins = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
            for groupe in groupes_sanguins:
                if not ReferentielGroupeSanguin.query.filter_by(libelle=groupe).first():
                    db.session.add(ReferentielGroupeSanguin(libelle=groupe))
            
            # 3. Catégories militaires
            categories = [
                {'libelle': 'Officier', 'description': 'Personnel officier'},
                {'libelle': 'Officier des rangs', 'description': 'Personnel sous-officier'},
                {'libelle': 'Militaire des rangs', 'description': 'Personnel militaire du rang'}
            ]
            for cat in categories:
                if not ReferentielCategorie.query.filter_by(libelle=cat['libelle']).first():
                    db.session.add(ReferentielCategorie(**cat))
            
            # 4. Services/Armes
            services = [
                {'code_court': 'ART', 'libelle': 'Artillerie', 'description': 'Arme de l\'Artillerie'},
                {'code_court': 'BLD', 'libelle': 'Blindé', 'description': 'Arme Blindée'},
                {'code_court': 'INF', 'libelle': 'Infanterie', 'description': 'Arme de l\'Infanterie'},
                {'code_court': 'TRS', 'libelle': 'Transmission', 'description': 'Service des Transmissions'},
                {'code_court': 'INT', 'libelle': 'Intendance', 'description': 'Service de l\'Intendance'},
                {'code_court': 'CAV', 'libelle': 'Cavalerie', 'description': 'Arme de la Cavalerie'},
                {'code_court': 'SAN', 'libelle': 'Santé', 'description': 'Service de Santé'},
                {'code_court': 'MAT', 'libelle': 'Matériel', 'description': 'Service du Matériel'},
                {'code_court': 'GEN', 'libelle': 'Génie', 'description': 'Arme du Génie'}
            ]
            for service in services:
                if not ReferentielService.query.filter_by(code_court=service['code_court']).first():
                    db.session.add(ReferentielService(**service))
            
            db.session.commit()
            
            # 5. Spécialités (après commit des services)
            service_art = ReferentielService.query.filter_by(code_court='ART').first()
            if service_art:
                specialites_art = [
                    {'service_id': service_art.id_service, 'code': 'SOL-SOL', 'libelle': 'Artillerie Sol-Sol'},
                    {'service_id': service_art.id_service, 'code': 'SOL-AIR', 'libelle': 'Artillerie Sol-Air'}
                ]
                for spec in specialites_art:
                    if not ReferentielSpecialite.query.filter_by(code=spec['code']).first():
                        db.session.add(ReferentielSpecialite(**spec))
            
            # 6. Unités
            unites = [
                {'code': '1GAR', 'libelle': '1er Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '2GAR', 'libelle': '2ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '3GAR', 'libelle': '3ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '4GAR', 'libelle': '4ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '5GAR', 'libelle': '5ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '6GAR', 'libelle': '6ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '7GAR', 'libelle': '7ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '8GAR', 'libelle': '8ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '9GAR', 'libelle': '9ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '10GAR', 'libelle': '10ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '11GAR', 'libelle': '11ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '12GAR', 'libelle': '12ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '13GAR', 'libelle': '13ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '14GAR', 'libelle': '14ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '15GAR', 'libelle': '15ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '16GAR', 'libelle': '16ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '17GAR', 'libelle': '17ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '18GAR', 'libelle': '18ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '19GAR', 'libelle': '19ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '20GAR', 'libelle': '20ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '21GAR', 'libelle': '21ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '22GAR', 'libelle': '22ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '23GAR', 'libelle': '23ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '24GAR', 'libelle': '24ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '25GAR', 'libelle': '25ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': '26GAR', 'libelle': '26ème Groupe d\'Artillerie Royale', 'type_unite': 'Régiment'},
                {'code': 'INSPART', 'libelle': 'Inspection de l\'Artillerie', 'type_unite': 'Inspection'},
                {'code': 'ERART', 'libelle': 'École Royale d\'Artillerie', 'type_unite': 'Autre'},
                {'code': 'GSA', 'libelle': 'Groupe de Soutien d\'Artillerie', 'type_unite': 'Autre'},
                {'code': 'CFA', 'libelle': 'Centre de Formation d\'Artillerie', 'type_unite': 'Autre'},
                {'code': 'BUR1', 'libelle': 'Bureau 1 - Personnel', 'type_unite': 'Bureau'},
                {'code': 'BUR2', 'libelle': 'Bureau 2 - Renseignement', 'type_unite': 'Bureau'},
                {'code': 'BUR3', 'libelle': 'Bureau 3 - Opérations', 'type_unite': 'Bureau'},
                {'code': 'BUR4', 'libelle': 'Bureau 4 - Logistique', 'type_unite': 'Bureau'},
                {'code': 'BUR5', 'libelle': 'Bureau 5 - Planification', 'type_unite': 'Bureau'}
            ]
            for unite in unites:
                if not ReferentielUnite.query.filter_by(code=unite['code']).first():
                    db.session.add(ReferentielUnite(**unite))
            
            # 7. Grades (hiérarchie militaire)
            grades = [
                # Officiers généraux
                {'code_grade': 'GEN', 'libelle': 'Général', 'niveau': 100},
                {'code_grade': 'GBR', 'libelle': 'Général de Brigade', 'niveau': 95},
                {'code_grade': 'COL', 'libelle': 'Colonel', 'niveau': 90},
                {'code_grade': 'LCL', 'libelle': 'Lieutenant-Colonel', 'niveau': 85},
                {'code_grade': 'CDT', 'libelle': 'Commandant', 'niveau': 80},
                # Officiers subalternes
                {'code_grade': 'CPT', 'libelle': 'Capitaine', 'niveau': 75},
                {'code_grade': 'LTN', 'libelle': 'Lieutenant', 'niveau': 70},
                {'code_grade': 'SLT', 'libelle': 'Sous-Lieutenant', 'niveau': 65},
                {'code_grade': 'ASP', 'libelle': 'Aspirant', 'niveau': 60},
                # Sous-officiers
                {'code_grade': 'ADC', 'libelle': 'Adjudant-Chef', 'niveau': 55},
                {'code_grade': 'ADJ', 'libelle': 'Adjudant', 'niveau': 50},
                {'code_grade': 'SGC', 'libelle': 'Sergent-Chef', 'niveau': 45},
                {'code_grade': 'SGT', 'libelle': 'Sergent', 'niveau': 40},
                # Militaires du rang
                {'code_grade': 'CPL', 'libelle': 'Caporal', 'niveau': 35},
                {'code_grade': 'CPLC', 'libelle': 'Caporal-Chef', 'niveau': 30},
                {'code_grade': 'SOL', 'libelle': 'Soldat', 'niveau': 25}
            ]
            for grade in grades:
                if not ReferentielGrade.query.filter_by(code_grade=grade['code_grade']).first():
                    db.session.add(ReferentielGrade(**grade))
            
            # 8. États matrimoniaux
            etats = [
                {'libelle': 'Célibataire', 'description': 'Personne non mariée'},
                {'libelle': 'Marié(e)', 'description': 'Personne mariée'},
                {'libelle': 'Divorcé(e)', 'description': 'Personne divorcée'},
                {'libelle': 'Veuf/Veuve', 'description': 'Personne veuve'}
            ]
            for etat in etats:
                if not ReferentielEtatMatrimonial.query.filter_by(libelle=etat['libelle']).first():
                    db.session.add(ReferentielEtatMatrimonial(**etat))
            
            # 9. Langues
            langues = [
                {'code_iso': 'FR', 'libelle': 'Français'},
                {'code_iso': 'EN', 'libelle': 'Anglais'},
                {'code_iso': 'ES', 'libelle': 'Espagnol'},
                {'code_iso': 'DE', 'libelle': 'Allemand'},
                {'code_iso': 'IT', 'libelle': 'Italien'},
                {'code_iso': 'PT', 'libelle': 'Portugais'},
                {'code_iso': 'RU', 'libelle': 'Russe'},
                {'code_iso': 'ZH', 'libelle': 'Chinois'}
            ]
            for langue in langues:
                if not ReferentielLangue.query.filter_by(code_iso=langue['code_iso']).first():
                    db.session.add(ReferentielLangue(**langue))
            
            # 10. Liens de parenté
            liens = [
                'Père', 'Mère', 'Frère', 'Sœur', 'Époux/Épouse', 'Fils', 'Fille',
                'Grand-père', 'Grand-mère', 'Oncle', 'Tante', 'Cousin', 'Cousine',
                'Beau-père', 'Belle-mère', 'Ami(e)', 'Autre'
            ]
            for lien in liens:
                if not ReferentielLienParente.query.filter_by(libelle=lien).first():
                    db.session.add(ReferentielLienParente(libelle=lien))
            
            # 11. Types d'absence
            types_absences = [
                'Permission ordinaire', 'Permission exceptionnelle', 'Congé de maladie',
                'Congé de maternité', 'Congé de paternité', 'Mission officielle',
                'Formation', 'Détachement', 'Congé sans solde', 'Autre'
            ]
            for type_abs in types_absences:
                if not ReferentielTypeAbsence.query.filter_by(libelle=type_abs).first():
                    db.session.add(ReferentielTypeAbsence(libelle=type_abs))
            
            db.session.commit()
            print("✅ Données de référence initialisées avec succès!")
            return True
    
    except Exception as e:
        db.session.rollback()
        print(f"❌ Erreur lors de l'initialisation des données: {str(e)}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Initialisation de la base de données RH militaire")
    print("=" * 60)
    
    # Création des tables
    if create_tables():
        # Initialisation des données de référence
        if init_referentiels():
            print("\n🎉 Base de données RH initialisée avec succès!")
            print("📋 25 tables créées")
            print("📊 Données de référence chargées")
            print("\n✅ L'application RH est prête à être utilisée!")
        else:
            print("\n❌ Erreur lors de l'initialisation des données de référence")
    else:
        print("\n❌ Erreur lors de la création des tables")

if __name__ == "__main__":
    main()
