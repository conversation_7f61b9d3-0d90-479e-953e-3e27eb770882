#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer les 25 tables RH selon l'architecture définie dans architecture_rh_militaire.md
Étape 3 du plan de reconstruction du système RH
"""

import mysql.connector
from mysql.connector import <PERSON>rror

def create_rh_tables():
    """Crée toutes les tables RH selon l'architecture spécifiée"""
    
    # Configuration de la base de données
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    # SQL pour créer les 25 tables RH
    tables_sql = [
        # 1. Tables de référence (11 tables)
        """
        CREATE TABLE referentiel_genre (
            id_genre INT PRIMARY KEY AUTO_INCREMENT,
            libelle VARCHAR(20) NOT NULL UNIQUE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_groupe_sanguin (
            id_groupe INT PRIMARY KEY AUTO_INCREMENT,
            libelle VARCHAR(3) NOT NULL UNIQUE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_categorie (
            id_categorie INT PRIMARY KEY AUTO_INCREMENT,
            libelle VARCHAR(50) NOT NULL UNIQUE,
            description TEXT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_service (
            id_service INT PRIMARY KEY AUTO_INCREMENT,
            code_court VARCHAR(10) NOT NULL UNIQUE,
            libelle VARCHAR(100) NOT NULL,
            description TEXT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_specialite (
            id_specialite INT PRIMARY KEY AUTO_INCREMENT,
            service_id INT NOT NULL,
            code VARCHAR(20) NOT NULL,
            libelle VARCHAR(100) NOT NULL,
            FOREIGN KEY (service_id) REFERENCES referentiel_service(id_service) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_unite (
            id_unite INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            libelle VARCHAR(150) NOT NULL,
            type_unite ENUM('Régiment', 'Inspection', 'Bureau', 'Autre') NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_grade (
            id_grade INT PRIMARY KEY AUTO_INCREMENT,
            code_grade VARCHAR(20) NOT NULL UNIQUE,
            libelle VARCHAR(50) NOT NULL,
            niveau INT NOT NULL,
            description TEXT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_etat_matrimonial (
            id_etat INT PRIMARY KEY AUTO_INCREMENT,
            libelle VARCHAR(20) NOT NULL UNIQUE,
            description TEXT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_langue (
            id_langue INT PRIMARY KEY AUTO_INCREMENT,
            code_iso CHAR(2) NOT NULL UNIQUE,
            libelle VARCHAR(50) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_lien_parente (
            id_lien INT PRIMARY KEY AUTO_INCREMENT,
            libelle VARCHAR(50) NOT NULL UNIQUE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE referentiel_type_absence (
            id_type INT PRIMARY KEY AUTO_INCREMENT,
            libelle VARCHAR(50) NOT NULL UNIQUE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        # 2. Table principale personnel (1 table)
        """
        CREATE TABLE personnel (
            matricule VARCHAR(20) PRIMARY KEY,
            nom VARCHAR(80) NOT NULL,
            prenom VARCHAR(80) NOT NULL,
            nom_ar VARCHAR(80) NOT NULL,
            prenom_ar VARCHAR(80) NOT NULL,
            date_naissance DATE NOT NULL,
            lieu_naissance VARCHAR(100) NOT NULL,
            genre_id INT NOT NULL,
            categorie_id INT NOT NULL,
            groupe_sanguin_id INT NOT NULL,
            cin_numero VARCHAR(20) NOT NULL,
            cin_date_delivrance DATE NOT NULL,
            cin_date_expiration DATE NOT NULL,
            gsm VARCHAR(20) NOT NULL,
            telephone_domicile VARCHAR(20) NULL,
            taille_cm INT NOT NULL,
            lieu_residence VARCHAR(200) NOT NULL,
            service_id INT NOT NULL,
            specialite_id INT NULL,
            unite_id INT NOT NULL,
            grade_actuel_id INT NOT NULL,
            fonction VARCHAR(100) NOT NULL,
            date_prise_fonction DATE NOT NULL,
            ccp_numero VARCHAR(50) NOT NULL,
            compte_bancaire_numero VARCHAR(50) NULL,
            somme_numero VARCHAR(50) NOT NULL,
            date_engagement DATE NOT NULL,
            nom_pere VARCHAR(80) NOT NULL,
            prenom_pere VARCHAR(80) NOT NULL,
            nom_mere VARCHAR(80) NOT NULL,
            prenom_mere VARCHAR(80) NOT NULL,
            adresse_parents VARCHAR(200) NOT NULL,
            etat_matrimonial_id INT NOT NULL,
            nombre_enfants INT NULL,
            passeport_numero VARCHAR(20) NULL,
            passeport_date_delivrance DATE NULL,
            passeport_date_expiration DATE NULL,
            gsm_urgence VARCHAR(20) NOT NULL,
            lien_parente_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (genre_id) REFERENCES referentiel_genre(id_genre),
            FOREIGN KEY (categorie_id) REFERENCES referentiel_categorie(id_categorie),
            FOREIGN KEY (groupe_sanguin_id) REFERENCES referentiel_groupe_sanguin(id_groupe),
            FOREIGN KEY (service_id) REFERENCES referentiel_service(id_service),
            FOREIGN KEY (specialite_id) REFERENCES referentiel_specialite(id_specialite),
            FOREIGN KEY (unite_id) REFERENCES referentiel_unite(id_unite),
            FOREIGN KEY (grade_actuel_id) REFERENCES referentiel_grade(id_grade),
            FOREIGN KEY (etat_matrimonial_id) REFERENCES referentiel_etat_matrimonial(id_etat),
            FOREIGN KEY (lien_parente_id) REFERENCES referentiel_lien_parente(id_lien)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        # 3. Table associative personnel_langue (1 table)
        """
        CREATE TABLE personnel_langue (
            personnel_matricule VARCHAR(20) NOT NULL,
            langue_id INT NOT NULL,
            PRIMARY KEY (personnel_matricule, langue_id),
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
            FOREIGN KEY (langue_id) REFERENCES referentiel_langue(id_langue) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        # 4. Table historique_grade (1 table)
        """
        CREATE TABLE historique_grade (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            grade_id INT NOT NULL,
            date_effet DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
            FOREIGN KEY (grade_id) REFERENCES referentiel_grade(id_grade)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        # 5. Tables familiales (2 tables)
        """
        CREATE TABLE conjoint (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            nom VARCHAR(80) NOT NULL,
            prenom VARCHAR(80) NOT NULL,
            nom_ar VARCHAR(80) NOT NULL,
            prenom_ar VARCHAR(80) NOT NULL,
            date_naissance DATE NOT NULL,
            lieu_naissance VARCHAR(100) NOT NULL,
            lieu_naissance_ar VARCHAR(100) NOT NULL,
            adresse VARCHAR(200) NOT NULL,
            adresse_ar VARCHAR(200) NOT NULL,
            date_mariage DATE NOT NULL,
            lieu_mariage VARCHAR(100) NOT NULL,
            profession VARCHAR(100) NOT NULL,
            profession_ar VARCHAR(100) NOT NULL,
            cin_numero VARCHAR(20) NOT NULL,
            gsm VARCHAR(20) NOT NULL,
            nom_pere VARCHAR(80) NOT NULL,
            prenom_pere VARCHAR(80) NOT NULL,
            nom_pere_ar VARCHAR(80) NOT NULL,
            prenom_pere_ar VARCHAR(80) NOT NULL,
            nom_mere VARCHAR(80) NOT NULL,
            prenom_mere VARCHAR(80) NOT NULL,
            nom_mere_ar VARCHAR(80) NOT NULL,
            prenom_mere_ar VARCHAR(80) NOT NULL,
            profession_pere VARCHAR(100) NOT NULL,
            profession_mere VARCHAR(100) NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,
        
        """
        CREATE TABLE enfant (
            id INT PRIMARY KEY AUTO_INCREMENT,
            conjoint_id INT NOT NULL,
            nom VARCHAR(80) NOT NULL,
            prenom VARCHAR(80) NOT NULL,
            sexe_id INT NOT NULL,
            date_naissance DATE NOT NULL,
            lieu_naissance VARCHAR(100) NOT NULL,
            date_deces DATE NULL,
            FOREIGN KEY (conjoint_id) REFERENCES conjoint(id) ON DELETE CASCADE,
            FOREIGN KEY (sexe_id) REFERENCES referentiel_genre(id_genre)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        # 6. Tables médicales (3 tables)
        """
        CREATE TABLE situation_medicale (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            maladies TEXT NOT NULL,
            date_hospitalisation DATE NOT NULL,
            lieu_hospitalisation VARCHAR(100) NOT NULL,
            aptitude ENUM('apte', 'innapte') NOT NULL,
            observations TEXT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE vaccination (
            id INT PRIMARY KEY AUTO_INCREMENT,
            situation_medicale_id INT NOT NULL,
            date_vaccination DATE NOT NULL,
            objet VARCHAR(100) NOT NULL,
            observations TEXT NULL,
            FOREIGN KEY (situation_medicale_id) REFERENCES situation_medicale(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE ptc (
            id INT PRIMARY KEY AUTO_INCREMENT,
            situation_medicale_id INT NOT NULL,
            date_ptc DATE NOT NULL,
            duree INT NOT NULL COMMENT 'en jours',
            date_debut DATE NOT NULL,
            date_fin DATE NOT NULL,
            objet VARCHAR(100) NOT NULL,
            observations TEXT NULL,
            FOREIGN KEY (situation_medicale_id) REFERENCES situation_medicale(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        # 7. Tables d'absences (3 tables)
        """
        CREATE TABLE absence_desertion (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            date_absence DATE NOT NULL,
            date_desertion DATE NOT NULL,
            date_retour DATE NOT NULL,
            date_arret_solde DATE NOT NULL,
            date_prise_solde DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE absence_detachement (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            date_debut DATE NOT NULL,
            adresse VARCHAR(200) NOT NULL,
            pays VARCHAR(100) NOT NULL,
            date_fin DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE absence_permission (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            date_debut DATE NOT NULL,
            date_fin DATE NOT NULL,
            adresse VARCHAR(200) NOT NULL,
            numero_serie VARCHAR(50) NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        # 8. Tables de mouvements et opérations (3 tables)
        """
        CREATE TABLE mouvement_interbie (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            unite_precedente_id INT NOT NULL,
            fonction VARCHAR(100) NOT NULL,
            date_debut DATE NOT NULL,
            date_fin DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
            FOREIGN KEY (unite_precedente_id) REFERENCES referentiel_unite(id_unite)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE sejour_ops (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            unite_id INT NOT NULL,
            date_debut DATE NOT NULL,
            date_fin DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
            FOREIGN KEY (unite_id) REFERENCES referentiel_unite(id_unite)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE liberation (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            motif VARCHAR(200) NOT NULL,
            date_liberation DATE NOT NULL,
            observations TEXT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        # 6. Tables médicales (3 tables)
        """
        CREATE TABLE situation_medicale (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            maladies TEXT NOT NULL,
            date_hospitalisation DATE NOT NULL,
            lieu_hospitalisation VARCHAR(100) NOT NULL,
            aptitude ENUM('apte', 'innapte') NOT NULL,
            observations TEXT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE vaccination (
            id INT PRIMARY KEY AUTO_INCREMENT,
            situation_medicale_id INT NOT NULL,
            date_vaccination DATE NOT NULL,
            objet VARCHAR(100) NOT NULL,
            observations TEXT NULL,
            FOREIGN KEY (situation_medicale_id) REFERENCES situation_medicale(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE ptc (
            id INT PRIMARY KEY AUTO_INCREMENT,
            situation_medicale_id INT NOT NULL,
            date_ptc DATE NOT NULL,
            duree INT NOT NULL COMMENT 'en jours',
            date_debut DATE NOT NULL,
            date_fin DATE NOT NULL,
            objet VARCHAR(100) NOT NULL,
            observations TEXT NULL,
            FOREIGN KEY (situation_medicale_id) REFERENCES situation_medicale(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        # 7. Tables d'absences (3 tables)
        """
        CREATE TABLE absence_desertion (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            date_absence DATE NOT NULL,
            date_desertion DATE NOT NULL,
            date_retour DATE NOT NULL,
            date_arret_solde DATE NOT NULL,
            date_prise_solde DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE absence_detachement (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            date_debut DATE NOT NULL,
            adresse VARCHAR(200) NOT NULL,
            pays VARCHAR(100) NOT NULL,
            date_fin DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE absence_permission (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            date_debut DATE NOT NULL,
            date_fin DATE NOT NULL,
            adresse VARCHAR(200) NOT NULL,
            numero_serie VARCHAR(50) NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        # 8. Tables de mouvements et opérations (3 tables)
        """
        CREATE TABLE mouvement_interbie (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            unite_precedente_id INT NOT NULL,
            fonction VARCHAR(100) NOT NULL,
            date_debut DATE NOT NULL,
            date_fin DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
            FOREIGN KEY (unite_precedente_id) REFERENCES referentiel_unite(id_unite)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE sejour_ops (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            unite_id INT NOT NULL,
            date_debut DATE NOT NULL,
            date_fin DATE NOT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
            FOREIGN KEY (unite_id) REFERENCES referentiel_unite(id_unite)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """,

        """
        CREATE TABLE liberation (
            id INT PRIMARY KEY AUTO_INCREMENT,
            personnel_matricule VARCHAR(20) NOT NULL,
            motif VARCHAR(200) NOT NULL,
            date_liberation DATE NOT NULL,
            observations TEXT NULL,
            FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
    ]
    
    try:
        # Connexion à la base de données
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("Connexion à la base de données réussie")
        print("Création des 25 tables RH en cours...")
        
        # Désactiver les contraintes de clés étrangères temporairement
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0;")
        
        # Créer chaque table
        for i, table_sql in enumerate(tables_sql, 1):
            try:
                cursor.execute(table_sql)
                table_name = table_sql.split('CREATE TABLE ')[1].split(' (')[0].strip()
                print(f"✓ Table {i}/25 créée : {table_name}")
            except Error as e:
                table_name = table_sql.split('CREATE TABLE ')[1].split(' (')[0].strip()
                print(f"✗ Erreur lors de la création de la table {table_name}: {e}")
        
        # Réactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1;")
        
        # Valider les changements
        connection.commit()
        print("\n✓ Toutes les tables RH ont été créées avec succès !")
        
    except Error as e:
        print(f"Erreur de connexion à la base de données : {e}")
        return False
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("Connexion fermée")
    
    return True

if __name__ == "__main__":
    print("=== ÉTAPE 3 : CRÉATION DES TABLES RH ===")
    print("Création des 25 tables selon architecture_rh_militaire.md")
    print("-" * 50)
    
    success = create_rh_tables()
    
    if success:
        print("\n🎉 Étape 3 terminée avec succès !")
        print("Les 25 tables RH ont été créées dans la base de données.")
    else:
        print("\n❌ Échec de l'étape 3")
        print("Vérifiez les erreurs ci-dessus.")
