"""
Script d'ajout des nouveaux référentiels sans supprimer les existants
Ajoute les unités et grades selon les spécifications
"""

from app import app
from db import db
from gestion_vehicules.rh.models import ReferentielUnite, ReferentielGrade

def ajouter_nouvelles_unites():
    """Ajoute les nouvelles unités selon spécifications"""
    try:
        with app.app_context():
            print("🏢 Ajout des nouvelles unités...")
            
            # Nouvelles unités selon spécifications
            unites_data = [
                # GAR (1 à 26)
                ('1GAR', '1er Groupe d\'Artillerie'),
                ('2GAR', '2ème Groupe d\'Artillerie'),
                ('3GAR', '3ème Groupe d\'Artillerie'),
                ('4GAR', '4ème Groupe d\'Artillerie'),
                ('5GAR', '5ème Groupe d\'Artillerie'),
                ('6GAR', '6ème Groupe d\'Artillerie'),
                ('7GAR', '7ème Groupe d\'Artillerie'),
                ('8GAR', '8ème Groupe d\'Artillerie'),
                ('9GAR', '9ème Groupe d\'Artillerie'),
                ('10GAR', '10ème Groupe d\'Artillerie'),
                ('11GAR', '11ème Groupe d\'Artillerie'),
                ('12GAR', '12ème Groupe d\'Artillerie'),
                ('13GAR', '13ème Groupe d\'Artillerie'),
                ('14GAR', '14ème Groupe d\'Artillerie'),
                ('15GAR', '15ème Groupe d\'Artillerie'),
                ('16GAR', '16ème Groupe d\'Artillerie'),
                ('17GAR', '17ème Groupe d\'Artillerie'),
                ('18GAR', '18ème Groupe d\'Artillerie'),
                ('19GAR', '19ème Groupe d\'Artillerie'),
                ('20GAR', '20ème Groupe d\'Artillerie'),
                ('21GAR', '21ème Groupe d\'Artillerie'),
                ('22GAR', '22ème Groupe d\'Artillerie'),
                ('23GAR', '23ème Groupe d\'Artillerie'),
                ('24GAR', '24ème Groupe d\'Artillerie'),
                ('25GAR', '25ème Groupe d\'Artillerie'),
                ('26GAR', '26ème Groupe d\'Artillerie'),
                
                # Unités spécialisées
                ('INSPART', 'Inspection de l\'Artillerie'),
                ('ERART', 'École Royale d\'Artillerie'),
                ('GSA', 'Groupement de Soutien de l\'Artillerie'),
                ('CFA', 'Centre de Formation de l\'Artillerie'),
                
                # Bureaux (1 à 5)
                ('1BUR', '1er Bureau'),
                ('2BUR', '2ème Bureau'),
                ('3BUR', '3ème Bureau'),
                ('4BUR', '4ème Bureau'),
                ('5BUR', '5ème Bureau'),
                
                # Bureaux spécialisés
                ('BREC', 'Bureau de Recrutement'),
                ('BCOUR', 'Bureau Courrier'),
                ('DPO', 'Direction du Personnel et de l\'Organisation'),
                ('PCA', 'Poste de Commandement Avancé'),
                
                # États-majors
                ('EMZS', 'État-Major Zone Sud'),
                ('EMZE', 'État-Major Zone Est'),
                
                # Services opérationnels
                ('SOPTAF', 'Service Opérationnel des Forces Terrestres'),
                ('SOPSAG', 'Service Opérationnel de la Sécurité et de la Garde'),
                ('SORIENT', 'Service Oriental'),
                
                # Autre
                ('AUTRE', 'Autre')
            ]
            
            ajoutees = 0
            for i, (code, libelle) in enumerate(unites_data, 1):
                # Vérifier si l'unité existe déjà
                existe = ReferentielUnite.query.filter_by(code=code).first()
                if not existe:
                    # Déterminer le type d'unité
                    if 'GAR' in code:
                        type_unite = 'Régiment'
                    elif 'INSPART' in code or 'ERART' in code:
                        type_unite = 'Inspection'
                    elif 'BUR' in code or 'Bureau' in libelle:
                        type_unite = 'Bureau'
                    else:
                        type_unite = 'Autre'

                    unite = ReferentielUnite(
                        code=code,
                        libelle=libelle,
                        type_unite=type_unite
                    )
                    db.session.add(unite)
                    ajoutees += 1
                else:
                    # Mettre à jour le libellé si différent
                    if existe.libelle != libelle:
                        existe.libelle = libelle
            
            db.session.commit()
            print(f"✅ {ajoutees} nouvelles unités ajoutées")
            
    except Exception as e:
        print(f"❌ Erreur ajout unités: {str(e)}")
        db.session.rollback()

def ajouter_nouveaux_grades():
    """Ajoute les nouveaux grades selon spécifications"""
    try:
        with app.app_context():
            print("🎖️ Ajout des nouveaux grades...")
            
            # Nouveaux grades selon spécifications
            grades_data = [
                ('SOL1', 'Soldat 1ère Classe', 1),
                ('SOL2', 'Soldat 2ème Classe', 2),
                ('BRG', 'Brigadier', 3),
                ('BRGC', 'Brigadier Chef', 4),
                ('MDL', 'MDL', 5),
                ('MDLC', 'MDL Chef', 6),
                ('ADJ', 'Adjudant', 7),
                ('ADJC', 'Adjudant Chef', 8),
                ('SLT', 'Sous-Lieutenant', 9),
                ('LTN', 'Lieutenant', 10),
                ('CPT', 'Capitaine', 11),
                ('CDT', 'Commandant', 12),
                ('LCL', 'Lieutenant-Colonel', 13),
                ('COL', 'Colonel', 14)
            ]
            
            ajoutees = 0
            for code_grade, libelle, niveau in grades_data:
                # Vérifier si le grade existe déjà
                existe = ReferentielGrade.query.filter_by(code_grade=code_grade).first()
                if not existe:
                    grade = ReferentielGrade(
                        code_grade=code_grade,
                        libelle=libelle,
                        niveau=niveau,
                        description=f"Grade: {libelle}"
                    )
                    db.session.add(grade)
                    ajoutees += 1
                else:
                    # Mettre à jour si différent
                    if existe.libelle != libelle:
                        existe.libelle = libelle
                        existe.niveau = niveau
                        existe.description = f"Grade: {libelle}"
            
            db.session.commit()
            print(f"✅ {ajoutees} nouveaux grades ajoutés")
            
    except Exception as e:
        print(f"❌ Erreur ajout grades: {str(e)}")
        db.session.rollback()

def afficher_referentiels():
    """Affiche tous les référentiels disponibles"""
    try:
        with app.app_context():
            print("\n📊 RÉFÉRENTIELS DISPONIBLES:")
            print("=" * 60)
            
            # Unités
            unites = ReferentielUnite.query.order_by(ReferentielUnite.code).all()
            print(f"🏢 UNITÉS ({len(unites)}):")
            
            # Grouper par type
            gar_unites = [u for u in unites if 'GAR' in u.code]
            bureau_unites = [u for u in unites if 'BUR' in u.code]
            autres_unites = [u for u in unites if 'GAR' not in u.code and 'BUR' not in u.code]
            
            if gar_unites:
                print("   📍 Groupes d'Artillerie:")
                for unite in gar_unites[:10]:  # Premiers 10
                    print(f"      • {unite.code}: {unite.libelle}")
                if len(gar_unites) > 10:
                    print(f"      ... et {len(gar_unites) - 10} autres GAR")
            
            if bureau_unites:
                print("   📍 Bureaux:")
                for unite in bureau_unites:
                    print(f"      • {unite.code}: {unite.libelle}")
            
            if autres_unites:
                print("   📍 Autres unités:")
                for unite in autres_unites:
                    print(f"      • {unite.code}: {unite.libelle}")
            
            # Grades
            grades = ReferentielGrade.query.order_by(ReferentielGrade.niveau).all()
            print(f"\n🎖️ GRADES ({len(grades)}):")
            for grade in grades:
                print(f"   • {grade.code_grade}: {grade.libelle}")
            
            # Personnel
            from gestion_vehicules.rh.models import Personnel
            total_personnel = Personnel.query.count()
            print(f"\n👥 PERSONNEL: {total_personnel} militaires")
            
    except Exception as e:
        print(f"❌ Erreur affichage: {str(e)}")

def main():
    """Fonction principale"""
    print("➕ AJOUT DES NOUVEAUX RÉFÉRENTIELS RH")
    print("=" * 60)
    print("📋 Ajout selon spécifications:")
    print("   • Unités: 1GAR-26GAR, INSPART, ERART, Bureaux, etc.")
    print("   • Grades: Soldat 1ère classe → Colonel")
    print("   • Conservation des données existantes")
    print("=" * 60)
    
    print("\n🚀 Début de l'ajout...")
    
    # Ajout
    ajouter_nouvelles_unites()
    ajouter_nouveaux_grades()
    
    # Affichage
    afficher_referentiels()
    
    print("\n🎉 AJOUT TERMINÉ!")
    print("🔗 Testez maintenant:")
    print("   • Recherche: http://localhost:3000/rh/recherche")
    print("   • Nouveau militaire: http://localhost:3000/rh/nouveau_militaire")
    print("\n✅ Vous devriez voir toutes les nouvelles options dans les listes déroulantes!")

if __name__ == "__main__":
    main()
