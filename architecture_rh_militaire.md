# Architecture de la base de données RH militaire

## 1. Tables de référence (dictionnaires de valeurs)

Chaque table de référence stocke un ensemble de valeurs stables et réutilisables dans tout le schéma. Elles facilitent la gestion centralisée des listes déroulantes, la cohérence des données, et l’ajout futur de nouvelles valeurs sans modification du modèle.

---

### 1.1 `referentiel_genre`  
**Objet :** liste des sexes biologiques  
| Colonne      | Type            | Contraintes                                 | Description                                             |
|--------------|-----------------|---------------------------------------------|---------------------------------------------------------|
| `id_genre`   | INT             | PRIMARY KEY, AUTO_INCREMENT                 | Identifiant unique                                       |
| `libelle`    | VARCHAR(20)     | NOT NULL, UNIQUE                            | ‘Masculin’, ‘Féminin’                                   |

---

### 1.2 `referentiel_groupe_sanguin`  
**Objet :** liste des groupes sanguins  
| Colonne        | Type        | Contraintes                         | Description                         |
|----------------|-------------|-------------------------------------|-------------------------------------|
| `id_groupe`    | INT         | PRIMARY KEY, AUTO_INCREMENT         | Identifiant unique                  |
| `libelle`      | VARCHAR(3)  | NOT NULL, UNIQUE                    | ‘A+’, ‘A–’, ‘B+’, ‘B–’, ‘AB+’, ‘AB–’, ‘O+’, ‘O–’ |

---

### 1.3 `referentiel_categorie`  
**Objet :** catégories militaires  
| Colonne           | Type         | Contraintes                         | Description                                        |
|-------------------|--------------|-------------------------------------|----------------------------------------------------|
| `id_categorie`    | INT          | PRIMARY KEY, AUTO_INCREMENT         | Identifiant                                        |
| `libelle`         | VARCHAR(50)  | NOT NULL, UNIQUE                    | ‘Officier’, ‘Militaire du rang’, etc.             |
| `description`     | TEXT         | NULL                                | Explication éventuelle (ex. prérogatives)          |

---

### 1.4 `referentiel_service`  
**Objet :** armes et services  
| Colonne           | Type          | Contraintes                         | Description                                   |
|-------------------|---------------|-------------------------------------|-----------------------------------------------|
| `id_service`      | INT           | PRIMARY KEY, AUTO_INCREMENT         | Identifiant                                   |
| `code_court`      | VARCHAR(10)   | NOT NULL, UNIQUE                    | Abréviation (ex. ‘ART’, ‘INF’, etc.)          |
| `libelle`         | VARCHAR(100)  | NOT NULL                            | ‘Artillerie’, ‘Infanterie’, etc.              |
| `description`     | TEXT          | NULL                                | Rôle et spécificités du service               |

---

### 1.5 `referentiel_specialite`  
**Objet :** spécialités rattachées à un service  
| Colonne            | Type         | Contraintes                                     | Description                 |
|--------------------|--------------|-------------------------------------------------|-----------------------------|
| `id_specialite`    | INT          | PRIMARY KEY, AUTO_INCREMENT                     | Identifiant                 |
| `service_id`       | INT          | NOT NULL, FK → `referentiel_service`.`id_service` | Service parent             |
| `code`             | VARCHAR(20)  | NOT NULL                                        | Code interne (ex. ‘SS’)     |
| `libelle`          | VARCHAR(100) | NOT NULL                                        | ‘Sol‑sol’, ‘Sol‑air’, etc.  |

---

### 1.6 `referentiel_unite`  
**Objet :** unités et bureaux  
| Colonne         | Type           | Contraintes                          | Description                             |
|-----------------|----------------|--------------------------------------|-----------------------------------------|
| `id_unite`      | INT            | PRIMARY KEY, AUTO_INCREMENT          | Identifiant                             |
| `code`          | VARCHAR(20)    | NOT NULL, UNIQUE                     | ‘1GAR’, ‘26GAR’, ‘ERART’, etc.          |
| `libelle`       | VARCHAR(150)   | NOT NULL                             | Nom complet de l’unité                  |
| `type_unite`    | ENUM           | NOT NULL                             | ‘Régiment’, ‘Inspection’, ‘Bureau’, ‘Autre’ |

---

### 1.7 `referentiel_grade`  
**Objet :** échelle hiérarchique des grades  
| Colonne         | Type          | Contraintes                          | Description                             |
|-----------------|---------------|--------------------------------------|-----------------------------------------|
| `id_grade`      | INT           | PRIMARY KEY, AUTO_INCREMENT          | Identifiant                             |
| `code_grade`    | VARCHAR(20)   | NOT NULL, UNIQUE                     | ‘S1C’, ‘BRI’, ‘LCL’, etc.               |
| `libelle`       | VARCHAR(50)   | NOT NULL                             | ‘Soldat 1re classe’, ‘Brigadier’, etc.  |
| `niveau`        | INT           | NOT NULL                             | Rang (1 = plus bas → N = plus haut)      |
| `description`   | TEXT          | NULL                                 | Conditions d’avancement, prérogatives   |

---

### 1.8 `referentiel_etat_matrimonial`  
**Objet :** états matrimoniaux  
| Colonne        | Type          | Contraintes                         | Description                                   |
|----------------|---------------|-------------------------------------|-----------------------------------------------|
| `id_etat`      | INT           | PRIMARY KEY, AUTO_INCREMENT         | Identifiant                                   |
| `libelle`      | VARCHAR(20)   | NOT NULL, UNIQUE                    | ‘Célibataire’, ‘Marié(e)’, ‘Divorcé(e)’, …    |
| `description`  | TEXT          | NULL                                | Notes éventuelles                             |

---

### 1.9 `referentiel_langue`  
**Objet :** langues parlées  
| Colonne        | Type      | Contraintes                         | Description                      |
|----------------|-----------|-------------------------------------|----------------------------------|
| `id_langue`    | INT       | PRIMARY KEY, AUTO_INCREMENT         | Identifiant                      |
| `code_iso`     | CHAR(2)   | NOT NULL, UNIQUE                    | ‘FR’, ‘EN’, ‘DE’, etc.           |
| `libelle`      | VARCHAR(50)| NOT NULL                            | ‘Français’, ‘Anglais’, etc.      |

---

### 1.10 `referentiel_lien_parente`  
**Objet :** degrés de parenté pour contact d’urgence  
| Colonne        | Type          | Contraintes                         | Description                      |
|----------------|---------------|-------------------------------------|----------------------------------|
| `id_lien`      | INT           | PRIMARY KEY, AUTO_INCREMENT         | Identifiant                      |
| `libelle`      | VARCHAR(50)   | NOT NULL, UNIQUE                    | ‘Père’, ‘Mère’, ‘Fils’, etc.     |

---

### 1.11 `referentiel_type_absence`  
**Objet :** catégories d’absence  
| Colonne        | Type          | Contraintes                         | Description                       |
|----------------|---------------|-------------------------------------|-----------------------------------|
| `id_type`      | INT           | PRIMARY KEY, AUTO_INCREMENT         | Identifiant                       |
| `libelle`      | VARCHAR(50)   | NOT NULL, UNIQUE                    | ‘Désertion’, ‘Détachement’, ‘Permission’ |

---

## 2. Table principale : `personnel`

Stocke l’ensemble des informations civiles et militaires d’un soldat.

| Colonne                     | Type            | Contraintes                                     |
|-----------------------------|-----------------|-------------------------------------------------|
| `matricule`                 | VARCHAR(20)     | PRIMARY KEY                                     |
| `nom` / `prenom`            | VARCHAR(80)     | NOT NULL                                        |
| `nom_ar` / `prenom_ar`      | VARCHAR(80)     | NOT NULL                                        |
| `date_naissance`            | DATE            | NOT NULL                                        |
| `lieu_naissance`            | VARCHAR(100)    | NOT NULL                                        |
| `genre_id`                  | INT             | FK → `referentiel_genre`.`id_genre`             |
| `categorie_id`              | INT             | FK → `referentiel_categorie`.`id_categorie`     |
| `groupe_sanguin_id`         | INT             | FK → `referentiel_groupe_sanguin`.`id_groupe`   |
| `cin_numero`                | VARCHAR(20)     | NOT NULL                                        |
| `cin_date_delivrance`       | DATE            | NOT NULL                                        |
| `cin_date_expiration`       | DATE            | NOT NULL                                        |
| `gsm`                       | VARCHAR(20)     | NOT NULL                                        |
| `telephone_domicile`        | VARCHAR(20)     | NULL                                            |
| `taille_cm`                 | INT             | NOT NULL                                        |
| `lieu_residence`            | VARCHAR(200)    | NOT NULL                                        |
| `service_id`                | INT             | FK → `referentiel_service`.`id_service`         |
| `specialite_id`             | INT             | FK → `referentiel_specialite`.`id_specialite`   |
| `unite_id`                  | INT             | FK → `referentiel_unite`.`id_unite`             |
| `grade_actuel_id`           | INT             | FK → `referentiel_grade`.`id_grade`             |
| `fonction`                  | VARCHAR(100)    | NOT NULL                                        |
| `date_prise_fonction`       | DATE            | NOT NULL                                        |
| `ccp_numero`                | VARCHAR(50)     | NOT NULL                                        |
| `compte_bancaire_numero`    | VARCHAR(50)     | NULL                                            |
| `somme_numero`              | VARCHAR(50)     | NOT NULL                                        |
| `date_engagement`           | DATE            | NOT NULL                                        |
| `nom_pere` / `prenom_pere`  | VARCHAR(80)     | NOT NULL                                        |
| `nom_mere` / `prenom_mere`  | VARCHAR(80)     | NOT NULL                                        |
| `adresse_parents`           | VARCHAR(200)    | NOT NULL                                        |
| `etat_matrimonial_id`       | INT             | FK → `referentiel_etat_matrimonial`.`id_etat`   |
| `nombre_enfants`            | INT             | NULL                                            |
| `passeport_numero`          | VARCHAR(20)     | NULL                                            |
| `passeport_date_delivrance` | DATE            | NULL                                            |
| `passeport_date_expiration` | DATE            | NULL                                            |
| `gsm_urgence`               | VARCHAR(20)     | NOT NULL                                        |
| `lien_parente_id`           | INT             | FK → `referentiel_lien_parente`.`id_lien`       |
| `created_at`                | TIMESTAMP       | DEFAULT CURRENT_TIMESTAMP                      |
| `updated_at`                | TIMESTAMP       | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

---

## 3. Langues parlées

### `personnel_langue`  
Table associative many-to-many entre `personnel` et `referentiel_langue`.

| Colonne             | Type         | Contraintes                                     |
|---------------------|--------------|-------------------------------------------------|
| `personnel_matricule` | VARCHAR(20)  | FK → `personnel`.`matricule`                   |
| `langue_id`         | INT          | FK → `referentiel_langue`.`id_langue`           |
| **PRIMARY KEY**     | (`personnel_matricule`, `langue_id`)                   |

---

## 4. Historique des grades

### `historique_grade`  
Enregistre chaque promotion.

| Colonne             | Type         | Contraintes                                     |
|---------------------|--------------|-------------------------------------------------|
| `id`                | INT          | PRIMARY KEY, AUTO_INCREMENT                     |
| `personnel_matricule` | VARCHAR(20) | FK → `personnel`.`matricule`                   |
| `grade_id`          | INT          | FK → `referentiel_grade`.`id_grade`             |
| `date_effet`        | DATE         | NOT NULL                                        |

---

## 5. Situation familiale

### `conjoint`  
Détail du conjoint si `etat_matrimonial = 'Marié(e)'`.

| Colonne             | Type         | Contraintes                                     |
|---------------------|--------------|-------------------------------------------------|
| `id`                | INT          | PRIMARY KEY, AUTO_INCREMENT                     |
| `personnel_matricule` | VARCHAR(20) | FK → `personnel`.`matricule`                   |
| `nom` / `prenom`    | VARCHAR(80)  | NOT NULL                                        |
| `nom_ar` / `prenom_ar` | VARCHAR(80)| NOT NULL                                        |
| `date_naissance`    | DATE         | NOT NULL                                        |
| `lieu_naissance`    | VARCHAR(100) | NOT NULL                                        |
| `lieu_naissance_ar` | VARCHAR(100) | NOT NULL                                        |
| `adresse` / `adresse_ar` | VARCHAR(200)| NOT NULL                                   |
| `date_mariage`      | DATE         | NOT NULL                                        |
| `lieu_mariage`      | VARCHAR(100)| NOT NULL                                         |
| `profession` / `profession_ar` | VARCHAR(100)| NOT NULL                            |
| `cin_numero`        | VARCHAR(20)  | NOT NULL                                        |
| `gsm`               | VARCHAR(20)  | NOT NULL                                        |
| `nom_pere` / `prenom_pere` | VARCHAR(80)| NOT NULL                              |
| `nom_pere_ar` / `prenom_pere_ar` | VARCHAR(80)| NOT NULL                      |
| `nom_mere` / `prenom_mere` | VARCHAR(80)| NOT NULL                              |
| `nom_mere_ar` / `prenom_mere_ar` | VARCHAR(80)| NOT NULL                      |
| `profession_pere`   | VARCHAR(100) | NOT NULL                                        |
| `profession_mere`   | VARCHAR(100) | NOT NULL                                        |

### `enfant`  
Enfants liés à un `conjoint`.

| Colonne             | Type        | Contraintes                                      |
|---------------------|-------------|--------------------------------------------------|
| `id`                | INT         | PRIMARY KEY, AUTO_INCREMENT                      |
| `conjoint_id`       | INT         | FK → `conjoint`.`id`                             |
| `nom` / `prenom`    | VARCHAR(80) | NOT NULL                                         |
| `sexe_id`           | INT         | FK → `referentiel_genre`.`id_genre`              |
| `date_naissance`    | DATE        | NOT NULL                                         |
| `lieu_naissance`    | VARCHAR(100)| NOT NULL                                         |
| `date_deces`        | DATE        | NULL                                             |

---

## 6. Situation médicale

### `situation_medicale`  
Fiche médicale globale.

| Colonne               | Type        | Contraintes                                      |
|-----------------------|-------------|--------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                      |
| `personnel_matricule` | VARCHAR(20) | FK → `personnel`.`matricule`                     |
| `maladies`            | TEXT        | NOT NULL                                         |
| `date_hospitalisation`| DATE        | NOT NULL                                         |
| `lieu_hospitalisation`| VARCHAR(100)| NOT NULL                                         |
| `aptitude`            | ENUM        | (‘apte’, ‘innapte’) NOT NULL                     |
| `observations`        | TEXT        | NULL                                             |

### `vaccination`  
Vaccinations successives.

| Colonne               | Type        | Contraintes                                      |
|-----------------------|-------------|--------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                      |
| `situation_medicale_id` | INT       | FK → `situation_medicale`.`id`                   |
| `date_vaccination`    | DATE        | NOT NULL                                         |
| `objet`               | VARCHAR(100)| NOT NULL                                         |
| `observations`        | TEXT        | NULL                                             |

### `ptc`  
Périodes de traitement/cotation.

| Colonne               | Type        | Contraintes                                      |
|-----------------------|-------------|--------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                      |
| `situation_medicale_id` | INT       | FK → `situation_medicale`.`id`                   |
| `date_ptc`            | DATE        | NOT NULL                                         |
| `duree`               | INT         | NOT NULL (en jours)                              |
| `date_debut`          | DATE        | NOT NULL                                         |
| `date_fin`            | DATE        | NOT NULL                                         |
| `objet`               | VARCHAR(100)| NOT NULL                                         |
| `observations`        | TEXT        | NULL                                             |

---

## 7. Absences

### `absence_desertion`  
| Colonne               | Type        | ContraConstraints                                  |
|-----------------------|-------------|----------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                        |
| `personnel_matricule` | VARCHAR(20)| FK → `personnel`.`matricule`                       |
| `date_absence`        | DATE        | NOT NULL                                           |
| `date_desertion`      | DATE        | NOT NULL                                           |
| `date_retour`         | DATE        | NOT NULL                                           |
| `date_arret_solde`    | DATE        | NOT NULL                                           |
| `date_prise_solde`    | DATE        | NOT NULL                                           |

### `absence_detachement`  
| Colonne               | Type        | ContraConstraints                                  |
|-----------------------|-------------|----------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                        |
| `personnel_matricule` | VARCHAR(20)| FK → `personnel`.`matricule`                       |
| `date_debut`          | DATE        | NOT NULL                                           |
| `adresse`             | VARCHAR(200)| NOT NULL                                           |
| `pays`                | VARCHAR(100)| NOT NULL                                           |
| `date_fin`            | DATE        | NOT NULL                                           |

### `absence_permission`  
| Colonne               | Type        | ContraConstraints                                  |
|-----------------------|-------------|----------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                        |
| `personnel_matricule` | VARCHAR(20)| FK → `personnel`.`matricule`                       |
| `date_debut`          | DATE        | NOT NULL                                           |
| `date_fin`            | DATE        | NOT NULL                                           |
| `adresse`             | VARCHAR(200)| NOT NULL                                           |
| `numero_serie`        | VARCHAR(50)| NOT NULL                                           |

---

## 8. Mouvements et opérations

### `mouvement_interbie`  
| Colonne               | Type        | ContraConstraints                                  |
|-----------------------|-------------|----------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                        |
| `personnel_matricule` | VARCHAR(20)| FK → `personnel`.`matricule`                       |
| `unite_precedente_id` | INT         | FK → `referentiel_unite`.`id_unite`                |
| `fonction`            | VARCHAR(100)| NOT NULL                                           |
| `date_debut`          | DATE        | NOT NULL                                           |
| `date_fin`            | DATE        | NOT NULL                                           |

### `sejour_ops`  
| Colonne               | Type        | ContraConstraints                                  |
|-----------------------|-------------|----------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                        |
| `personnel_matricule` | VARCHAR(20)| FK → `personnel`.`matricule`                       |
| `unite_id`            | INT         | FK → `referentiel_unite`.`id_unite`                |
| `date_debut`          | DATE        | NOT NULL                                           |
| `date_fin`            | DATE        | NOT NULL                                           |

### `liberation`  
| Colonne               | Type        | ContraConstraints                                  |
|-----------------------|-------------|----------------------------------------------------|
| `id`                  | INT         | PRIMARY KEY, AUTO_INCREMENT                        |
| `personnel_matricule` | VARCHAR(20)| FK → `personnel`.`matricule`                       |
| `motif`               | VARCHAR(200)| NOT NULL                                          |
| `date_liberation`     | DATE        | NOT NULL                                           |
| `observations`        | TEXT        | NULL                                              |

---

## 9. Diagramme simplifié des relations

```
referentiel_* 1—∞ personnel
personnel 1—∞ historique_grade
personnel 1—∞ conjoint  —∞ enfant
personnel 1—∞ situation_medicale —1—∞ vaccination  
                              └—1—∞ ptc
personnel 1—∞ absence_* (desertion, detachement, permission)
personnel 1—∞ mouvement_interbie
personnel 1—∞ sejour_ops
personnel 1—∞ liberation
personnel ∞—∞ referentiel_langue (via personnel_langue)
```

*Cette architecture assure la modularité, la traçabilité des évolutions (grades, fonctions, absences…), et la facilité d’ajout de nouvelles valeurs de référence (grades, unités, services, etc.).*
