{% extends "rh/base_rh.html" %}

{% block title %}Gestion Conjoint - {{ militaire.nom_complet }}{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-heart"></i>
                                Gestion du Conjoint
                            </h2>
                            <small style="color: var(--text-light);">
                                Militaire : {{ militaire.nom_complet }} ({{ militaire.matricule }})
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à la Fiche
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire Conjoint -->
    <form method="POST" class="needs-validation" novalidate>
        <div class="row">
            <!-- Informations Personnelles du Conjoint -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations Personnelles
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom *</label>
                                <input type="text" name="nom" class="form-control" required 
                                       value="{{ conjoint.nom if conjoint else '' }}">
                                <div class="invalid-feedback">Le nom est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom *</label>
                                <input type="text" name="prenom" class="form-control" required 
                                       value="{{ conjoint.prenom if conjoint else '' }}">
                                <div class="invalid-feedback">Le prénom est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom (Arabe) *</label>
                                <input type="text" name="nom_ar" class="form-control" required dir="rtl"
                                       value="{{ conjoint.nom_ar if conjoint else '' }}">
                                <div class="invalid-feedback">Le nom en arabe est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom (Arabe) *</label>
                                <input type="text" name="prenom_ar" class="form-control" required dir="rtl"
                                       value="{{ conjoint.prenom_ar if conjoint else '' }}">
                                <div class="invalid-feedback">Le prénom en arabe est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date de Naissance *</label>
                                <input type="date" name="date_naissance" class="form-control" required 
                                       value="{{ conjoint.date_naissance.strftime('%Y-%m-%d') if conjoint and conjoint.date_naissance else '' }}">
                                <div class="invalid-feedback">La date de naissance est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Lieu de Naissance *</label>
                                <input type="text" name="lieu_naissance" class="form-control" required 
                                       value="{{ conjoint.lieu_naissance if conjoint else '' }}">
                                <div class="invalid-feedback">Le lieu de naissance est obligatoire</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents d'Identité -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-id-card"></i>
                            Documents d'Identité
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Numéro CIN *</label>
                                <input type="text" name="cin_numero" class="form-control" required 
                                       value="{{ conjoint.cin_numero if conjoint else '' }}">
                                <div class="invalid-feedback">Le numéro CIN est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date de Délivrance CIN *</label>
                                <input type="date" name="cin_date_delivrance" class="form-control" required 
                                       value="{{ conjoint.cin_date_delivrance.strftime('%Y-%m-%d') if conjoint and conjoint.cin_date_delivrance else '' }}">
                                <div class="invalid-feedback">La date de délivrance est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date d'Expiration CIN *</label>
                                <input type="date" name="cin_date_expiration" class="form-control" required 
                                       value="{{ conjoint.cin_date_expiration.strftime('%Y-%m-%d') if conjoint and conjoint.cin_date_expiration else '' }}">
                                <div class="invalid-feedback">La date d'expiration est obligatoire</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Informations Familiales -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i>
                            Informations Familiales
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom du Père *</label>
                                <input type="text" name="nom_pere" class="form-control" required 
                                       value="{{ conjoint.nom_pere if conjoint else '' }}">
                                <div class="invalid-feedback">Le nom du père est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom du Père *</label>
                                <input type="text" name="prenom_pere" class="form-control" required 
                                       value="{{ conjoint.prenom_pere if conjoint else '' }}">
                                <div class="invalid-feedback">Le prénom du père est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom de la Mère *</label>
                                <input type="text" name="nom_mere" class="form-control" required 
                                       value="{{ conjoint.nom_mere if conjoint else '' }}">
                                <div class="invalid-feedback">Le nom de la mère est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom de la Mère *</label>
                                <input type="text" name="prenom_mere" class="form-control" required 
                                       value="{{ conjoint.prenom_mere if conjoint else '' }}">
                                <div class="invalid-feedback">Le prénom de la mère est obligatoire</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations Professionnelles et Mariage -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-briefcase"></i>
                            Profession et Mariage
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Profession</label>
                                <input type="text" name="profession" class="form-control" 
                                       value="{{ conjoint.profession if conjoint else '' }}"
                                       placeholder="Ex: Enseignante, Infirmière, Sans profession...">
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Lieu de Travail</label>
                                <input type="text" name="lieu_travail" class="form-control" 
                                       value="{{ conjoint.lieu_travail if conjoint else '' }}"
                                       placeholder="Nom de l'établissement ou entreprise">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">GSM</label>
                                <input type="tel" name="gsm" class="form-control" 
                                       value="{{ conjoint.gsm if conjoint else '' }}"
                                       placeholder="06XXXXXXXX">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date de Mariage *</label>
                                <input type="date" name="date_mariage" class="form-control" required 
                                       value="{{ conjoint.date_mariage.strftime('%Y-%m-%d') if conjoint and conjoint.date_mariage else '' }}">
                                <div class="invalid-feedback">La date de mariage est obligatoire</div>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Lieu de Mariage *</label>
                                <input type="text" name="lieu_mariage" class="form-control" required 
                                       value="{{ conjoint.lieu_mariage if conjoint else '' }}"
                                       placeholder="Ville et pays du mariage">
                                <div class="invalid-feedback">Le lieu de mariage est obligatoire</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boutons d'Action -->
        <div class="row">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-success-military btn-lg me-3">
                            <i class="fas fa-save"></i> 
                            {{ 'Mettre à Jour' if conjoint else 'Enregistrer' }} le Conjoint
                        </button>
                        <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Validation du numéro de téléphone marocain
function validateMoroccanPhone(input) {
    const phonePattern = /^(06|07|05)\d{8}$/;
    if (input.value && !phonePattern.test(input.value.replace(/\s/g, ''))) {
        input.setCustomValidity('Format invalide (ex: 06XXXXXXXX)');
    } else {
        input.setCustomValidity('');
    }
}

document.querySelectorAll('input[type="tel"]').forEach(input => {
    input.addEventListener('input', () => validateMoroccanPhone(input));
});

// Auto-complétion intelligente
document.addEventListener('DOMContentLoaded', function() {
    // Si c'est une modification, calculer l'âge du conjoint
    const dateNaissance = document.querySelector('input[name="date_naissance"]');
    if (dateNaissance.value) {
        const age = calculateAge(new Date(dateNaissance.value));
        console.log('Âge du conjoint:', age, 'ans');
    }
    
    dateNaissance.addEventListener('change', function() {
        if (this.value) {
            const age = calculateAge(new Date(this.value));
            console.log('Âge du conjoint:', age, 'ans');
        }
    });
});

function calculateAge(birthDate) {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    
    return age;
}
</script>
{% endblock %}
