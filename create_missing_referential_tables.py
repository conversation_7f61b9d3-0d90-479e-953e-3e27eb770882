#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Création des tables référentielles manquantes
"""

import mysql.connector
from mysql.connector import Error

def create_missing_tables():
    """Crée les tables référentielles manquantes"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("🔧 Création des tables référentielles manquantes...")
        
        # Tables référentielles manquantes
        tables_sql = [
            # Table referentiel_categorie
            """
            CREATE TABLE IF NOT EXISTS referentiel_categorie (
                id_categorie INT PRIMARY KEY AUTO_INCREMENT,
                libelle VARCHAR(50) NOT NULL UNIQUE,
                description TEXT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table referentiel_groupe_sanguin
            """
            CREATE TABLE IF NOT EXISTS referentiel_groupe_sanguin (
                id_groupe INT PRIMARY KEY AUTO_INCREMENT,
                libelle VARCHAR(10) NOT NULL UNIQUE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table referentiel_service
            """
            CREATE TABLE IF NOT EXISTS referentiel_service (
                id_service INT PRIMARY KEY AUTO_INCREMENT,
                libelle VARCHAR(100) NOT NULL UNIQUE,
                code_service VARCHAR(20) NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table referentiel_specialite
            """
            CREATE TABLE IF NOT EXISTS referentiel_specialite (
                id_specialite INT PRIMARY KEY AUTO_INCREMENT,
                libelle VARCHAR(100) NOT NULL UNIQUE,
                code_specialite VARCHAR(20) NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table referentiel_etat_matrimonial
            """
            CREATE TABLE IF NOT EXISTS referentiel_etat_matrimonial (
                id_etat INT PRIMARY KEY AUTO_INCREMENT,
                libelle VARCHAR(30) NOT NULL UNIQUE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table referentiel_lien_parente
            """
            CREATE TABLE IF NOT EXISTS referentiel_lien_parente (
                id_lien INT PRIMARY KEY AUTO_INCREMENT,
                libelle VARCHAR(50) NOT NULL UNIQUE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """
        ]
        
        # Créer chaque table
        for i, table_sql in enumerate(tables_sql, 1):
            try:
                cursor.execute(table_sql)
                table_name = table_sql.split('CREATE TABLE IF NOT EXISTS ')[1].split(' (')[0].strip()
                print(f"✓ Table {i}/6 créée : {table_name}")
            except Error as e:
                table_name = table_sql.split('CREATE TABLE IF NOT EXISTS ')[1].split(' (')[0].strip()
                print(f"✗ Erreur table {table_name}: {e}")
        
        # Peupler les tables avec des données de base
        print("\n📊 Peuplement des tables référentielles...")
        
        # Catégories
        categories = [
            ('Officier', 'Personnel officier'),
            ('Sous-Officier', 'Personnel sous-officier'),
            ('Militaire du rang', 'Personnel militaire du rang')
        ]
        
        for libelle, description in categories:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_categorie (libelle, description)
                VALUES (%s, %s)
            """, (libelle, description))
        
        # Groupes sanguins
        groupes_sanguins = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
        for groupe in groupes_sanguins:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_groupe_sanguin (libelle)
                VALUES (%s)
            """, (groupe,))
        
        # Services
        services = [
            ('Artillerie', 'ART'),
            ('Infanterie', 'INF'),
            ('Génie', 'GEN'),
            ('Transmissions', 'TRA'),
            ('Logistique', 'LOG'),
            ('Administration', 'ADM'),
            ('Santé', 'SAN')
        ]
        
        for libelle, code in services:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_service (libelle, code_service)
                VALUES (%s, %s)
            """, (libelle, code))
        
        # Spécialités
        specialites = [
            ('Canonnier', 'CAN'),
            ('Mécanicien', 'MEC'),
            ('Conducteur', 'CON'),
            ('Secrétaire', 'SEC'),
            ('Cuisinier', 'CUI'),
            ('Infirmier', 'INF'),
            ('Électricien', 'ELE')
        ]
        
        for libelle, code in specialites:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_specialite (libelle, code_specialite)
                VALUES (%s, %s)
            """, (libelle, code))
        
        # États matrimoniaux
        etats = ['Célibataire', 'Marié(e)', 'Divorcé(e)', 'Veuf(ve)']
        for etat in etats:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_etat_matrimonial (libelle)
                VALUES (%s)
            """, (etat,))
        
        # Liens de parenté
        liens = ['Père', 'Mère', 'Frère', 'Sœur', 'Époux/Épouse', 'Fils', 'Fille', 'Autre']
        for lien in liens:
            cursor.execute("""
                INSERT IGNORE INTO referentiel_lien_parente (libelle)
                VALUES (%s)
            """, (lien,))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n✅ Tables référentielles créées et peuplées avec succès !")
        return True
        
    except Error as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    print("🎯 CRÉATION DES TABLES RÉFÉRENTIELLES MANQUANTES")
    print("=" * 60)
    
    success = create_missing_tables()
    
    if success:
        print("\n🎉 Tables référentielles créées avec succès !")
        print("L'erreur ReferentielCategorie devrait être corrigée.")
    else:
        print("\n❌ Échec de la création des tables")
