#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Étape 4 - Partie 1: Peupler les tables de référence avec les données exactes
Selon les spécifications du fichier appliquer_specifications_exactes.py
"""

from app import app
from db import db
from gestion_vehicules.rh.models import *

def populate_referentiels():
    """Peuple toutes les tables de référence avec les données exactes"""
    
    with app.app_context():
        try:
            print("🎯 ÉTAPE 4 - PARTIE 1: PEUPLEMENT DES RÉFÉRENTIELS")
            print("=" * 60)
            
            # 1. Genres
            print("👥 Création des genres...")
            genres = [
                {'libelle': 'Masculin'},
                {'libelle': 'Féminin'}
            ]
            for genre_data in genres:
                if not ReferentielGenre.query.filter_by(libelle=genre_data['libelle']).first():
                    genre = ReferentielGenre(**genre_data)
                    db.session.add(genre)
            db.session.commit()
            print(f"   ✓ {len(genres)} genres créés")
            
            # 2. Groupes sanguins
            print("🩸 Création des groupes sanguins...")
            groupes = [
                {'libelle': 'A+'},
                {'libelle': 'A-'},
                {'libelle': 'B+'},
                {'libelle': 'B-'},
                {'libelle': 'AB+'},
                {'libelle': 'AB-'},
                {'libelle': 'O+'},
                {'libelle': 'O-'}
            ]
            for groupe_data in groupes:
                if not ReferentielGroupeSanguin.query.filter_by(libelle=groupe_data['libelle']).first():
                    groupe = ReferentielGroupeSanguin(**groupe_data)
                    db.session.add(groupe)
            db.session.commit()
            print(f"   ✓ {len(groupes)} groupes sanguins créés")
            
            # 3. Catégories
            print("📋 Création des catégories...")
            categories = [
                {'libelle': 'Officier', 'description': 'Personnel officier'},
                {'libelle': 'Sous-officier', 'description': 'Personnel sous-officier'},
                {'libelle': 'Militaire du rang', 'description': 'Personnel militaire du rang'}
            ]
            for cat_data in categories:
                if not ReferentielCategorie.query.filter_by(libelle=cat_data['libelle']).first():
                    categorie = ReferentielCategorie(**cat_data)
                    db.session.add(categorie)
            db.session.commit()
            print(f"   ✓ {len(categories)} catégories créées")
            
            # 4. Services
            print("🏢 Création des services...")
            services = [
                {'code_court': 'ART', 'libelle': 'Artillerie', 'description': 'Service de l\'Artillerie'},
                {'code_court': 'INF', 'libelle': 'Infanterie', 'description': 'Service de l\'Infanterie'},
                {'code_court': 'CAV', 'libelle': 'Cavalerie', 'description': 'Service de la Cavalerie'},
                {'code_court': 'GEN', 'libelle': 'Génie', 'description': 'Service du Génie'},
                {'code_court': 'LOG', 'libelle': 'Logistique', 'description': 'Service Logistique'},
                {'code_court': 'ADM', 'libelle': 'Administration', 'description': 'Service Administratif'}
            ]
            for service_data in services:
                if not ReferentielService.query.filter_by(code_court=service_data['code_court']).first():
                    service = ReferentielService(**service_data)
                    db.session.add(service)
            db.session.commit()
            print(f"   ✓ {len(services)} services créés")
            
            # 5. Spécialités (liées aux services)
            print("🎯 Création des spécialités...")
            # Récupérer les services créés
            service_art = ReferentielService.query.filter_by(code_court='ART').first()
            service_inf = ReferentielService.query.filter_by(code_court='INF').first()
            service_adm = ReferentielService.query.filter_by(code_court='ADM').first()
            
            specialites = [
                {'service_id': service_art.id_service, 'code': 'ART_155', 'libelle': 'Artillerie 155mm'},
                {'service_id': service_art.id_service, 'code': 'ART_105', 'libelle': 'Artillerie 105mm'},
                {'service_id': service_inf.id_service, 'code': 'INF_COM', 'libelle': 'Infanterie de Combat'},
                {'service_id': service_adm.id_service, 'code': 'ADM_RH', 'libelle': 'Administration RH'},
                {'service_id': service_adm.id_service, 'code': 'ADM_FIN', 'libelle': 'Administration Financière'}
            ]
            for spec_data in specialites:
                if not ReferentielSpecialite.query.filter_by(code=spec_data['code']).first():
                    specialite = ReferentielSpecialite(**spec_data)
                    db.session.add(specialite)
            db.session.commit()
            print(f"   ✓ {len(specialites)} spécialités créées")
            
            # 6. Grades (EXACTEMENT selon appliquer_specifications_exactes.py)
            print("🎖️ Création des grades (spécifications exactes)...")
            # Supprimer tous les grades existants
            ReferentielGrade.query.delete()
            db.session.commit()
            
            grades_exacts = [
                ('SOL1', 'Soldat 1ère Classe', 1),
                ('SOL2', 'Soldat 2ème Classe', 2),
                ('BRG', 'Brigadier', 3),
                ('BRGC', 'Brigadier Chef', 4),
                ('MDL', 'MDL', 5),
                ('MDLC', 'MDL Chef', 6),
                ('ADJ', 'Adjudant', 7),
                ('ADJC', 'Adjudant Chef', 8),
                ('SLT', 'Sous-Lieutenant', 9),
                ('LTN', 'Lieutenant', 10),
                ('CPT', 'Capitaine', 11),
                ('CDT', 'Commandant', 12),
                ('LCL', 'Lieutenant-Colonel', 13),
                ('COL', 'Colonel', 14)
            ]
            
            for code_grade, libelle, niveau in grades_exacts:
                grade = ReferentielGrade(
                    code_grade=code_grade,
                    libelle=libelle,
                    niveau=niveau,
                    description=f"Grade: {libelle}"
                )
                db.session.add(grade)
            db.session.commit()
            print(f"   ✓ {len(grades_exacts)} grades créés (spécifications exactes)")
            
            # 7. Unités (EXACTEMENT selon appliquer_specifications_exactes.py)
            print("🏢 Création des unités (spécifications exactes)...")
            # Supprimer toutes les unités existantes
            ReferentielUnite.query.delete()
            db.session.commit()
            
            unites_exactes = []
            
            # 1GAR au 26GAR
            for i in range(1, 27):
                unites_exactes.append((f'{i}GAR', f'{i}GAR', 'Régiment'))
            
            # Unités spécialisées dans l'ordre exact
            unites_exactes.extend([
                ('INSPART', 'Inspection de l\'Artillerie', 'Inspection'),
                ('ERART', 'ERART', 'Autre'),
                ('GSA', 'GSA', 'Autre'),
                ('CFA', 'CFA', 'Autre'),
                ('1BUR', '1er Bureau', 'Bureau'),
                ('2BUR', '2ème Bureau', 'Bureau'),
                ('3BUR', '3ème Bureau', 'Bureau'),
                ('4BUR', '4ème Bureau', 'Bureau'),
                ('5BUR', '5ème Bureau', 'Bureau'),
                ('BREC', 'Bureau de Recrutement', 'Bureau'),
                ('BCOUR', 'Bureau Courrier', 'Bureau'),
                ('DPO', 'DPO', 'Autre'),
                ('PCA', 'PCA', 'Autre'),
                ('EMZS', 'État-Major Zone Sud', 'Autre'),
                ('EMZE', 'État-Major Zone Est', 'Autre'),
                ('SOPTAF', 'SOPTAF', 'Autre'),
                ('SOPSAG', 'SOPSAG', 'Autre'),
                ('SORIENT', 'S.ORIENTAL', 'Autre'),
                ('AUTRE', 'Autre', 'Autre')
            ])
            
            for code, libelle, type_unite in unites_exactes:
                unite = ReferentielUnite(
                    code=code,
                    libelle=libelle,
                    type_unite=type_unite
                )
                db.session.add(unite)
            db.session.commit()
            print(f"   ✓ {len(unites_exactes)} unités créées (spécifications exactes)")
            
            # 8. États matrimoniaux
            print("💒 Création des états matrimoniaux...")
            etats = [
                {'libelle': 'Célibataire', 'description': 'Personne non mariée'},
                {'libelle': 'Marié(e)', 'description': 'Personne mariée'},
                {'libelle': 'Divorcé(e)', 'description': 'Personne divorcée'},
                {'libelle': 'Veuf/Veuve', 'description': 'Personne veuve'}
            ]
            for etat_data in etats:
                if not ReferentielEtatMatrimonial.query.filter_by(libelle=etat_data['libelle']).first():
                    etat = ReferentielEtatMatrimonial(**etat_data)
                    db.session.add(etat)
            db.session.commit()
            print(f"   ✓ {len(etats)} états matrimoniaux créés")
            
            # 9. Langues
            print("🗣️ Création des langues...")
            langues = [
                {'code_iso': 'AR', 'libelle': 'Arabe'},
                {'code_iso': 'FR', 'libelle': 'Français'},
                {'code_iso': 'EN', 'libelle': 'Anglais'},
                {'code_iso': 'ES', 'libelle': 'Espagnol'},
                {'code_iso': 'DE', 'libelle': 'Allemand'}
            ]
            for langue_data in langues:
                if not ReferentielLangue.query.filter_by(code_iso=langue_data['code_iso']).first():
                    langue = ReferentielLangue(**langue_data)
                    db.session.add(langue)
            db.session.commit()
            print(f"   ✓ {len(langues)} langues créées")
            
            # 10. Liens de parenté
            print("👨‍👩‍👧‍👦 Création des liens de parenté...")
            liens = [
                {'libelle': 'Père'},
                {'libelle': 'Mère'},
                {'libelle': 'Frère'},
                {'libelle': 'Sœur'},
                {'libelle': 'Oncle'},
                {'libelle': 'Tante'},
                {'libelle': 'Cousin'},
                {'libelle': 'Cousine'},
                {'libelle': 'Ami proche'},
                {'libelle': 'Autre'}
            ]
            for lien_data in liens:
                if not ReferentielLienParente.query.filter_by(libelle=lien_data['libelle']).first():
                    lien = ReferentielLienParente(**lien_data)
                    db.session.add(lien)
            db.session.commit()
            print(f"   ✓ {len(liens)} liens de parenté créés")
            
            # 11. Types d'absence
            print("📅 Création des types d'absence...")
            types_absence = [
                {'libelle': 'Permission ordinaire'},
                {'libelle': 'Permission exceptionnelle'},
                {'libelle': 'Congé maladie'},
                {'libelle': 'Congé maternité'},
                {'libelle': 'Détachement'},
                {'libelle': 'Mission'},
                {'libelle': 'Formation'},
                {'libelle': 'Désertion'}
            ]
            for type_data in types_absence:
                if not ReferentielTypeAbsence.query.filter_by(libelle=type_data['libelle']).first():
                    type_abs = ReferentielTypeAbsence(**type_data)
                    db.session.add(type_abs)
            db.session.commit()
            print(f"   ✓ {len(types_absence)} types d'absence créés")
            
            print("\n✅ PARTIE 1 TERMINÉE AVEC SUCCÈS !")
            print("Tous les référentiels sont peuplés avec les données exactes.")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du peuplement: {e}")
            db.session.rollback()
            return False

if __name__ == "__main__":
    success = populate_referentiels()
    
    if success:
        print("\n🎉 Référentiels créés avec succès !")
        print("Prêt pour la création des données de test du personnel.")
    else:
        print("\n❌ Échec du peuplement des référentiels")
