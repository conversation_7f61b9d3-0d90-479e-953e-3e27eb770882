"""
Génération de données de test avec MULTITUDE de noms/prénoms différents
200 militaires avec noms/prénoms uniques et correspondance FR=AR exacte
CONSERVATION de toutes les fonctionnalités existantes
"""

import random
from datetime import datetime, date, timedelta
from app import app
from db import db
from gestion_vehicules.rh.models import *

# GRANDE VARIÉTÉ DE NOMS/PRÉNOMS avec correspondance FR-AR exacte
MILITAIRES_VARIES = [
    # Hommes - 100 combinaisons différentes
    ('ALAMI', 'MOHAMMED', 'العلمي', 'محمد', 'M'),
    ('BENALI', 'AHMED', 'بن علي', 'أحمد', 'M'),
    ('CHAKIR', 'HASSAN', 'شاكر', 'حسن', 'M'),
    ('DRISSI', 'OMAR', 'الدريسي', 'عمر', 'M'),
    ('EL FASSI', 'YOUSSEF', 'الفاسي', 'يوسف', 'M'),
    ('FILALI', 'KHALID', 'الفيلالي', 'خالد', 'M'),
    ('GHAZI', 'ABDELKADER', 'الغازي', 'عبد القادر', 'M'),
    ('HAJJI', 'MUSTAPHA', 'الحاجي', 'مصطفى', 'M'),
    ('IDRISSI', 'SAID', 'الإدريسي', 'سعيد', 'M'),
    ('JAMAL', 'RACHID', 'جمال', 'رشيد', 'M'),
    ('KADIRI', 'ABDELLAH', 'القادري', 'عبد الله', 'M'),
    ('LAHLOU', 'KARIM', 'اللحلو', 'كريم', 'M'),
    ('MANSOURI', 'NOUREDDINE', 'المنصوري', 'نور الدين', 'M'),
    ('NACIRI', 'ABDERRAHIM', 'الناصري', 'عبد الرحيم', 'M'),
    ('OUALI', 'DRISS', 'الوالي', 'إدريس', 'M'),
    ('QADIRI', 'FOUAD', 'القادري', 'فؤاد', 'M'),
    ('RACHIDI', 'JAMAL', 'الرشيدي', 'جمال', 'M'),
    ('SLIMANI', 'LARBI', 'السليماني', 'العربي', 'M'),
    ('TAZI', 'MBAREK', 'التازي', 'مبارك', 'M'),
    ('WAHBI', 'NABIL', 'الوهبي', 'نبيل', 'M'),
    ('ZAKI', 'OTHMANE', 'زكي', 'عثمان', 'M'),
    ('AMRANI', 'REDOUANE', 'العمراني', 'رضوان', 'M'),
    ('BERRADA', 'TARIK', 'برادة', 'طارق', 'M'),
    ('CHRAIBI', 'ZAKARIA', 'الشرايبي', 'زكريا', 'M'),
    ('DOUIRI', 'AMINE', 'الدويري', 'أمين', 'M'),
    ('EL ALAOUI', 'BRAHIM', 'العلوي', 'إبراهيم', 'M'),
    ('FASSI', 'HAMID', 'الفاسي', 'حميد', 'M'),
    ('GUERRAOUI', 'ISMAIL', 'الكراوي', 'إسماعيل', 'M'),
    ('HASSANI', 'MEHDI', 'الحسني', 'مهدي', 'M'),
    ('ISMAILI', 'SAMIR', 'الإسماعيلي', 'سمير', 'M'),
    ('JEBARI', 'AZIZ', 'الجباري', 'عزيز', 'M'),
    ('KETTANI', 'HICHAM', 'الكتاني', 'هشام', 'M'),
    ('LAMRANI', 'ADIL', 'اللمراني', 'عادل', 'M'),
    ('MEKOUAR', 'BADR', 'المكوار', 'بدر', 'M'),
    ('NEJJAR', 'BILAL', 'النجار', 'بلال', 'M'),
    ('OUAZZANI', 'CHAFIK', 'الوزاني', 'شفيق', 'M'),
    ('QORCHI', 'DAOUDI', 'القرشي', 'داودي', 'M'),
    ('RAISSOUNI', 'ELHADI', 'الريسوني', 'الهادي', 'M'),
    ('SEBTI', 'FARID', 'السبتي', 'فريد', 'M'),
    ('TOUNSI', 'GHALI', 'التونسي', 'غالي', 'M'),
    ('ALAOUI', 'HABIB', 'العلوي', 'حبيب', 'M'),
    ('BENKIRANE', 'ILYAS', 'بن كيران', 'إلياس', 'M'),
    ('CHERKAOUI', 'JAWAD', 'الشرقاوي', 'جواد', 'M'),
    ('DARIF', 'KHALIL', 'ضريف', 'خليل', 'M'),
    ('ELALAMI', 'LHOUCINE', 'العلمي', 'الحسين', 'M'),
    ('FATHI', 'MAHER', 'فتحي', 'ماهر', 'M'),
    ('GHALLAB', 'NASSER', 'غلاب', 'ناصر', 'M'),
    ('HARIRI', 'OUSSAMA', 'الحريري', 'أسامة', 'M'),
    ('IRAQI', 'PIERRE', 'العراقي', 'بيير', 'M'),
    ('JAOUI', 'QASIM', 'الجاوي', 'قاسم', 'M'),
    ('KABBAJ', 'RAMI', 'الكباج', 'رامي', 'M'),
    ('LAROUI', 'SALAM', 'العروي', 'سلام', 'M'),
    ('MAANINOU', 'TAHA', 'معنينو', 'طه', 'M'),
    ('NAJI', 'UMAR', 'ناجي', 'عمار', 'M'),
    ('OUAHBI', 'WALID', 'الوهبي', 'وليد', 'M'),
    ('QADRI', 'XAVIER', 'القادري', 'كزافييه', 'M'),
    ('RIFAI', 'YASSIR', 'الرفاعي', 'ياسر', 'M'),
    ('SOUSSI', 'ZAID', 'السوسي', 'زيد', 'M'),
    ('TAHIRI', 'ABDESSAMAD', 'الطاهري', 'عبد الصمد', 'M'),
    ('UYSAL', 'BACHIR', 'أويسال', 'بشير', 'M'),
    ('VIDAL', 'CHAKIB', 'فيدال', 'شكيب', 'M'),
    ('WARDI', 'DRISS', 'الوردي', 'إدريس', 'M'),
    ('YAZIDI', 'ELHOUCINE', 'اليزيدي', 'الحسين', 'M'),
    ('ZEMMOURI', 'FAOUZI', 'الزموري', 'فوزي', 'M'),
    
    # Femmes - 50 combinaisons différentes
    ('ALAMI', 'FATIMA', 'العلمي', 'فاطمة', 'F'),
    ('BENALI', 'AICHA', 'بن علي', 'عائشة', 'F'),
    ('CHAKIR', 'KHADIJA', 'شاكر', 'خديجة', 'F'),
    ('DRISSI', 'ZINEB', 'الدريسي', 'زينب', 'F'),
    ('EL FASSI', 'AMINA', 'الفاسي', 'أمينة', 'F'),
    ('FILALI', 'LATIFA', 'الفيلالي', 'لطيفة', 'F'),
    ('GHAZI', 'MALIKA', 'الغازي', 'مليكة', 'F'),
    ('HAJJI', 'NAIMA', 'الحاجي', 'نعيمة', 'F'),
    ('IDRISSI', 'RACHIDA', 'الإدريسي', 'رشيدة', 'F'),
    ('JAMAL', 'SAIDA', 'جمال', 'سعيدة', 'F'),
    ('KADIRI', 'HAFIDA', 'القادري', 'حفيدة', 'F'),
    ('LAHLOU', 'JAMILA', 'اللحلو', 'جميلة', 'F'),
    ('MANSOURI', 'KARIMA', 'المنصوري', 'كريمة', 'F'),
    ('NACIRI', 'LEILA', 'الناصري', 'ليلى', 'F'),
    ('OUALI', 'MERYEM', 'الوالي', 'مريم', 'F'),
    ('QADIRI', 'NADIA', 'القادري', 'نادية', 'F'),
    ('RACHIDI', 'OUAFAE', 'الرشيدي', 'وفاء', 'F'),
    ('SLIMANI', 'RAJAE', 'السليماني', 'رجاء', 'F'),
    ('TAZI', 'SAMIRA', 'التازي', 'سميرة', 'F'),
    ('WAHBI', 'ZAHRA', 'الوهبي', 'زهرة', 'F'),
    ('ZAKI', 'AMAL', 'زكي', 'أمل', 'F'),
    ('AMRANI', 'BOUCHRA', 'العمراني', 'بشرى', 'F'),
    ('BERRADA', 'CHAIMAE', 'برادة', 'شيماء', 'F'),
    ('CHRAIBI', 'DOUNIA', 'الشرايبي', 'دنيا', 'F'),
    ('DOUIRI', 'EMAN', 'الدويري', 'إيمان', 'F'),
    ('EL ALAOUI', 'FADWA', 'العلوي', 'فدوى', 'F'),
    ('FASSI', 'GHITA', 'الفاسي', 'غيتة', 'F'),
    ('GUERRAOUI', 'HANANE', 'الكراوي', 'حنان', 'F'),
    ('HASSANI', 'ILHAM', 'الحسني', 'إلهام', 'F'),
    ('ISMAILI', 'JIHANE', 'الإسماعيلي', 'جيهان', 'F'),
    ('JEBARI', 'KENZA', 'الجباري', 'كنزة', 'F'),
    ('KETTANI', 'LAMIAE', 'الكتاني', 'لمياء', 'F'),
    ('LAMRANI', 'MOUNA', 'اللمراني', 'منى', 'F'),
    ('MEKOUAR', 'NEZHA', 'المكوار', 'نزهة', 'F'),
    ('NEJJAR', 'OUIDAD', 'النجار', 'وداد', 'F'),
    ('OUAZZANI', 'PATRICIA', 'الوزاني', 'باتريسيا', 'F'),
    ('QORCHI', 'QAMAR', 'القرشي', 'قمر', 'F'),
    ('RAISSOUNI', 'RIM', 'الريسوني', 'ريم', 'F'),
    ('SEBTI', 'SALMA', 'السبتي', 'سلمى', 'F'),
    ('TOUNSI', 'TOURIA', 'التونسي', 'ثريا', 'F'),
    ('ALAOUI', 'UMNIA', 'العلوي', 'أمنية', 'F'),
    ('BENKIRANE', 'WIDAD', 'بن كيران', 'وداد', 'F'),
    ('CHERKAOUI', 'YASMINE', 'الشرقاوي', 'ياسمين', 'F'),
    ('DARIF', 'ZINEB', 'ضريف', 'زينب', 'F'),
    ('ELALAMI', 'ASMAE', 'العلمي', 'أسماء', 'F'),
    ('FATHI', 'BTISSAM', 'فتحي', 'ابتسام', 'F'),
    ('GHALLAB', 'CHADIA', 'غلاب', 'شادية', 'F'),
    ('HARIRI', 'DALILA', 'الحريري', 'دليلة', 'F'),
    ('IRAQI', 'EVELYNE', 'العراقي', 'إيفلين', 'F'),
    ('JAOUI', 'FATIHA', 'الجاوي', 'فتيحة', 'F'),
    ('KABBAJ', 'GHIZLANE', 'الكباج', 'غزلان', 'F')
]

VILLES_MAROC = [
    'Rabat', 'Casablanca', 'Fès', 'Marrakech', 'Agadir', 'Tanger', 'Meknès', 'Oujda', 
    'Kenitra', 'Tétouan', 'Safi', 'Mohammedia', 'Khouribga', 'Beni Mellal', 'El Jadida', 
    'Nador', 'Taza', 'Settat', 'Berrechid', 'Khemisset', 'Salé', 'Temara', 'Larache',
    'Ksar El Kebir', 'Guelmim', 'Errachidia', 'Ouarzazate', 'Tiznit', 'Essaouira', 'Chefchaouen'
]

def generer_matricule_unique(matricules_utilises):
    """Génère un matricule unique de 8 chiffres"""
    while True:
        matricule = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        if matricule not in matricules_utilises:
            matricules_utilises.add(matricule)
            return matricule

def generer_cin_unique(cins_utilises):
    """Génère un CIN unique format Lettre + 6 chiffres"""
    while True:
        lettre = random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')
        chiffres = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        cin = f"{lettre}{chiffres}"
        if cin not in cins_utilises:
            cins_utilises.add(cin)
            return cin

def generer_gsm():
    """Génère un GSM marocain"""
    prefixes = ['06', '07']
    return f"{random.choice(prefixes)}{''.join([str(random.randint(0, 9)) for _ in range(8)])}"

def supprimer_donnees_existantes():
    """Supprime UNIQUEMENT les données de personnel (CONSERVATION des référentiels)"""
    try:
        with app.app_context():
            print("🗑️ Suppression des données existantes (CONSERVATION des référentiels)...")
            
            # Supprimer dans l'ordre des dépendances
            PersonnelLangue.query.delete()
            HistoriqueGrade.query.delete()
            Sanction.query.delete()
            Liberation.query.delete()
            SejourOperationnel.query.delete()
            MutationInterBie.query.delete()
            Detachement.query.delete()
            Desertion.query.delete()
            Permission.query.delete()
            Enfant.query.delete()
            Conjoint.query.delete()
            PTC.query.delete()
            Vaccination.query.delete()
            Hospitalisation.query.delete()
            SituationMedicale.query.delete()
            Personnel.query.delete()
            
            db.session.commit()
            print("✅ Données existantes supprimées (référentiels conservés)")
            
    except Exception as e:
        print(f"❌ Erreur suppression: {str(e)}")
        db.session.rollback()

def creer_militaires_varies():
    """Crée 200 militaires avec noms/prénoms tous différents"""
    try:
        with app.app_context():
            print("🚀 Génération de 200 militaires avec noms/prénoms VARIÉS...")
            
            # Récupération des référentiels (CONSERVÉS)
            genres = {g.libelle: g.id_genre for g in ReferentielGenre.query.all()}
            categories = {c.libelle: c.id_categorie for c in ReferentielCategorie.query.all()}
            groupes_sanguins = [g.id_groupe for g in ReferentielGroupeSanguin.query.all()]
            services = [s.id_service for s in ReferentielService.query.all()]
            unites = [u.id_unite for u in ReferentielUnite.query.all()]
            grades_dict = {g.code_grade: g.id_grade for g in ReferentielGrade.query.all()}
            etats_matrimoniaux = [e.id_etat for e in ReferentielEtatMatrimonial.query.all()]
            liens_parente = [l.id_lien for l in ReferentielLienParente.query.all()]
            
            # Ensembles pour garantir l'unicité
            matricules_utilises = set()
            cins_utilises = set()
            militaires_crees = 0
            
            # Mélanger la liste pour éviter les répétitions
            militaires_melange = MILITAIRES_VARIES.copy()
            random.shuffle(militaires_melange)
            
            # Configuration par catégorie
            categories_config = [
                ('Officier', 50, 25, 55),
                ('Officier des rangs', 50, 22, 45),
                ('Militaire des rangs', 100, 18, 35)
            ]
            
            index_militaire = 0
            
            for cat_nom, nombre, age_min, age_max in categories_config:
                print(f"📊 Génération de {nombre} {cat_nom}s avec noms VARIÉS...")
                
                for i in range(nombre):
                    try:
                        # Sélection du militaire suivant (garantit la variété)
                        if index_militaire < len(militaires_melange):
                            militaire_data = militaires_melange[index_militaire]
                        else:
                            # Si on dépasse, recommencer avec un mélange différent
                            random.shuffle(militaires_melange)
                            militaire_data = militaires_melange[index_militaire % len(militaires_melange)]
                        
                        index_militaire += 1
                        
                        nom_fr, prenom_fr, nom_ar, prenom_ar, genre_code = militaire_data
                        
                        # Genre
                        genre_id = genres['Masculin'] if genre_code == 'M' else genres['Féminin']
                        
                        # Identifiants uniques
                        matricule = generer_matricule_unique(matricules_utilises)
                        cin_numero = generer_cin_unique(cins_utilises)
                        
                        # Dates
                        age = random.randint(age_min, age_max)
                        date_naissance = date(date.today().year - age, random.randint(1, 12), random.randint(1, 28))
                        
                        annees_service = random.randint(1, min(25, age - 18))
                        date_engagement = date(date.today().year - annees_service, random.randint(1, 12), random.randint(1, 28))
                        
                        cin_delivrance = date_naissance + timedelta(days=random.randint(6570, 7300))
                        cin_expiration = cin_delivrance + timedelta(days=random.randint(3650, 5475))
                        
                        # Création du militaire
                        militaire = Personnel(
                            matricule=matricule,
                            nom=nom_fr,
                            prenom=prenom_fr,
                            nom_ar=nom_ar,
                            prenom_ar=prenom_ar,
                            date_naissance=date_naissance,
                            lieu_naissance=random.choice(VILLES_MAROC),
                            genre_id=genre_id,
                            categorie_id=categories[cat_nom],
                            groupe_sanguin_id=random.choice(groupes_sanguins),
                            cin_numero=cin_numero,
                            cin_date_delivrance=cin_delivrance,
                            cin_date_expiration=cin_expiration,
                            gsm=generer_gsm(),
                            telephone_domicile=generer_gsm() if random.random() < 0.6 else None,
                            taille_cm=random.randint(155, 190),
                            lieu_residence=f"{random.randint(1, 999)} Rue {random.choice(['Hassan II', 'Mohammed V', 'Al Massira'])}, {random.choice(VILLES_MAROC)}",
                            service_id=random.choice(services),
                            specialite_id=None,
                            unite_id=random.choice(unites),
                            grade_actuel_id=random.choice(list(grades_dict.values())),
                            fonction=f"Fonction {random.choice(['Opérationnelle', 'Administrative', 'Technique'])}",
                            date_prise_fonction=date_engagement + timedelta(days=random.randint(0, 365)),
                            date_engagement=date_engagement,
                            ccp_numero=''.join([str(random.randint(0, 9)) for _ in range(12)]),
                            compte_bancaire_numero=''.join([str(random.randint(0, 9)) for _ in range(16)]) if random.random() < 0.7 else None,
                            somme_numero=f"SM{''.join([str(random.randint(0, 9)) for _ in range(8)])}",
                            nom_pere=random.choice([p[1] for p in MILITAIRES_VARIES if p[4] == 'M']),
                            prenom_pere=random.choice([p[1] for p in MILITAIRES_VARIES if p[4] == 'M']),
                            nom_mere=random.choice([p[1] for p in MILITAIRES_VARIES if p[4] == 'F']),
                            prenom_mere=random.choice([p[1] for p in MILITAIRES_VARIES if p[4] == 'F']),
                            adresse_parents=f"{random.choice(VILLES_MAROC)}, Maroc",
                            etat_matrimonial_id=random.choice(etats_matrimoniaux),
                            nombre_enfants=random.randint(0, 4) if random.random() < 0.6 else None,
                            passeport_numero=f"{''.join([chr(random.randint(65, 90)) for _ in range(2)])}{''.join([str(random.randint(0, 9)) for _ in range(6)])}" if random.random() < 0.4 else None,
                            passeport_date_delivrance=cin_delivrance + timedelta(days=random.randint(0, 1000)) if random.random() < 0.4 else None,
                            passeport_date_expiration=cin_expiration + timedelta(days=random.randint(0, 1000)) if random.random() < 0.4 else None,
                            gsm_urgence=generer_gsm(),
                            lien_parente_id=random.choice(liens_parente)
                        )
                        
                        db.session.add(militaire)
                        
                        # Situation médicale
                        situation = SituationMedicale(
                            matricule=matricule,
                            aptitude=random.choice(['Apte', 'Apte', 'Apte', 'Apte', 'Inapte']),
                            date_derniere_visite=date.today() - timedelta(days=random.randint(30, 365)),
                            observations_generales="Situation normale" if random.random() < 0.8 else "Suivi particulier"
                        )
                        db.session.add(situation)
                        
                        # Historique grade
                        historique = HistoriqueGrade(
                            matricule=matricule,
                            grade_id=militaire.grade_actuel_id,
                            date_debut=date_engagement,
                            numero_decision=f"DEC{random.randint(1000, 9999)}",
                            observations="Grade initial"
                        )
                        db.session.add(historique)
                        
                        militaires_crees += 1
                        
                        # Commit par batch de 25
                        if militaires_crees % 25 == 0:
                            db.session.commit()
                            print(f"✅ {militaires_crees} militaires créés...")
                            
                    except Exception as e:
                        print(f"⚠️ Erreur militaire {militaires_crees + 1}: {str(e)}")
                        db.session.rollback()
                        continue
            
            # Commit final
            db.session.commit()
            print(f"🎉 {militaires_crees} militaires créés avec noms/prénoms VARIÉS!")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur générale: {str(e)}")
        db.session.rollback()
        return False

def main():
    """Fonction principale"""
    print("🎯 GÉNÉRATION DE DONNÉES VARIÉES")
    print("=" * 60)
    print("📋 Objectifs:")
    print("   • 200 militaires avec noms/prénoms TOUS DIFFÉRENTS")
    print("   • Correspondance FR=AR exacte")
    print("   • CONSERVATION de toutes les fonctionnalités existantes")
    print("   • Situations variées (famille, médical, etc.)")
    print("=" * 60)
    
    print("\n🚀 Génération en cours...")
    
    # Suppression et recréation
    supprimer_donnees_existantes()
    
    if creer_militaires_varies():
        print("\n🎉 GÉNÉRATION TERMINÉE AVEC SUCCÈS!")
        print("📊 200 militaires avec noms/prénoms VARIÉS")
        print("🔗 Testez la recherche améliorée:")
        print("   • http://localhost:3000/rh/recherche")
        print("   • Essayez: 'ALAMI MOHAMMED', 'BENALI AHMED', etc.")
    else:
        print("\n❌ ERREUR LORS DE LA GÉNÉRATION")

if __name__ == "__main__":
    main()
