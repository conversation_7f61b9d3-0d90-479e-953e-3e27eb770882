#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Peuplement des tables originales du checkpoint 70 avec des données de test
Pour que les fiches de renseignement s'affichent correctement
"""

import mysql.connector
from mysql.connector import Error
import random
from datetime import datetime, date, timedelta

def populate_original_tables():
    """Peuple les tables originales avec des données de test"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("📊 Peuplement des tables originales du checkpoint 70...")
        
        # Récupérer les matricules existants
        cursor.execute("SELECT matricule FROM personnel LIMIT 20")
        matricules = [row[0] for row in cursor.fetchall()]
        
        if not matricules:
            print("❌ Aucun personnel trouvé dans la base de données")
            return False
        
        print(f"✓ {len(matricules)} militaires trouvés")
        
        # 1. Situations médicales
        print("1. Création des situations médicales...")
        for matricule in matricules:
            aptitude = random.choice(['Apte', 'Inapte'])
            date_visite = date.today() - timedelta(days=random.randint(30, 365))
            observations = f"Visite médicale du {date_visite.strftime('%d/%m/%Y')} - Aptitude: {aptitude}"
            
            cursor.execute("""
                INSERT INTO situation_medicale_original 
                (matricule, aptitude, date_derniere_visite, observations_generales)
                VALUES (%s, %s, %s, %s)
            """, (matricule, aptitude, date_visite, observations))
        
        # 2. Hospitalisations
        print("2. Création des hospitalisations...")
        for matricule in random.sample(matricules, min(10, len(matricules))):
            date_entree = date.today() - timedelta(days=random.randint(60, 730))
            date_sortie = date_entree + timedelta(days=random.randint(1, 15))
            etablissement = random.choice([
                'Hôpital Militaire Mohammed V',
                'Hôpital Militaire Avicenne',
                'Infirmerie de Garnison',
                'Centre Médical Régional'
            ])
            motif = random.choice([
                'Intervention chirurgicale',
                'Traitement médical',
                'Bilan de santé',
                'Accident de service'
            ])
            
            cursor.execute("""
                INSERT INTO hospitalisation_original 
                (matricule, date_entree, date_sortie, etablissement, motif, diagnostic)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (matricule, date_entree, date_sortie, etablissement, motif, f"Diagnostic pour {motif}"))
        
        # 3. Vaccinations
        print("3. Création des vaccinations...")
        vaccins = ['Tétanos', 'Hépatite B', 'Grippe', 'COVID-19', 'Fièvre Jaune']
        for matricule in matricules:
            for vaccin in random.sample(vaccins, random.randint(2, 4)):
                date_vaccination = date.today() - timedelta(days=random.randint(30, 1095))
                rappel = date_vaccination + timedelta(days=365) if vaccin != 'COVID-19' else None
                
                cursor.execute("""
                    INSERT INTO vaccination_original 
                    (matricule, nom_vaccin, date_vaccination, lieu_vaccination, rappel_prevu)
                    VALUES (%s, %s, %s, %s, %s)
                """, (matricule, vaccin, date_vaccination, 'Infirmerie de Garnison', rappel))
        
        # 4. PTC
        print("4. Création des PTC...")
        for matricule in random.sample(matricules, min(8, len(matricules))):
            date_debut = date.today() - timedelta(days=random.randint(30, 365))
            duree = random.randint(3, 30)
            date_fin = date_debut + timedelta(days=duree)
            objet = random.choice([
                'Congé de maladie',
                'Permission exceptionnelle',
                'Formation médicale',
                'Convalescence'
            ])
            
            cursor.execute("""
                INSERT INTO ptc_original 
                (matricule, date_debut, date_fin, duree_jours, objet, lieu_ptc)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (matricule, date_debut, date_fin, duree, objet, 'Domicile'))
        
        # 5. Conjoints
        print("5. Création des conjoints...")
        noms_conjoints = [
            ('ALAMI', 'Fatima', 'العلمي', 'فاطمة'),
            ('BENALI', 'Aicha', 'بن علي', 'عائشة'),
            ('TAZI', 'Khadija', 'التازي', 'خديجة'),
            ('IDRISSI', 'Malika', 'الإدريسي', 'مليكة'),
            ('ALAOUI', 'Zineb', 'العلوي', 'زينب')
        ]
        
        for matricule in random.sample(matricules, min(12, len(matricules))):
            nom, prenom, nom_ar, prenom_ar = random.choice(noms_conjoints)
            date_naissance = date(random.randint(1980, 1995), random.randint(1, 12), random.randint(1, 28))
            cin = f"K{random.randint(100000, 999999)}"
            date_mariage = date(random.randint(2010, 2020), random.randint(1, 12), random.randint(1, 28))
            
            cursor.execute("""
                INSERT INTO conjoint_original 
                (matricule, nom, prenom, nom_ar, prenom_ar, date_naissance, lieu_naissance,
                 cin_numero, cin_date_delivrance, cin_date_expiration, nom_pere, prenom_pere,
                 nom_mere, prenom_mere, date_mariage, lieu_mariage)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (matricule, nom, prenom, nom_ar, prenom_ar, date_naissance, 'Rabat',
                  cin, date(2015, 1, 1), date(2025, 1, 1), 'PERE', 'Prenom',
                  'MERE', 'Prenom', date_mariage, 'Rabat'))
        
        # 6. Enfants
        print("6. Création des enfants...")
        cursor.execute("SELECT matricule FROM conjoint_original")
        matricules_avec_conjoint = [row[0] for row in cursor.fetchall()]
        
        for matricule in matricules_avec_conjoint:
            nb_enfants = random.randint(0, 3)
            for i in range(nb_enfants):
                nom_enfant = f"ENFANT{i+1}"
                prenom_enfant = random.choice(['Ahmed', 'Mohammed', 'Fatima', 'Aicha', 'Omar'])
                genre_id = random.randint(1, 2)  # Supposant que 1=M, 2=F
                date_naissance = date(random.randint(2010, 2020), random.randint(1, 12), random.randint(1, 28))
                
                cursor.execute("""
                    INSERT INTO enfant_original 
                    (matricule, nom, prenom, nom_ar, prenom_ar, genre_id, date_naissance, lieu_naissance)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (matricule, nom_enfant, prenom_enfant, nom_enfant, prenom_enfant, 
                      genre_id, date_naissance, 'Rabat'))
        
        # 7. Permissions
        print("7. Création des permissions...")
        for matricule in matricules:
            nb_permissions = random.randint(1, 3)
            for _ in range(nb_permissions):
                date_debut = date.today() - timedelta(days=random.randint(30, 365))
                duree = random.randint(2, 15)
                date_fin = date_debut + timedelta(days=duree)
                
                cursor.execute("""
                    INSERT INTO permission_original 
                    (matricule, type_absence_id, date_debut, date_fin, duree_jours, motif)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (matricule, 1, date_debut, date_fin, duree, 'Permission ordinaire'))
        
        # 8. Historique des grades
        print("8. Création de l'historique des grades...")
        for matricule in matricules:
            # Grade actuel
            cursor.execute("SELECT grade_actuel_id FROM personnel WHERE matricule = %s", (matricule,))
            grade_actuel = cursor.fetchone()
            if grade_actuel:
                date_debut = date.today() - timedelta(days=random.randint(365, 1095))
                cursor.execute("""
                    INSERT INTO historique_grade_original 
                    (matricule, grade_id, date_debut, numero_decision)
                    VALUES (%s, %s, %s, %s)
                """, (matricule, grade_actuel[0], date_debut, f"DEC{random.randint(1000, 9999)}"))
        
        # 9. Langues du personnel
        print("9. Création des langues du personnel...")
        cursor.execute("SELECT id_langue FROM referentiel_langue LIMIT 5")
        langues_ids = [row[0] for row in cursor.fetchall()]
        
        for matricule in matricules:
            nb_langues = random.randint(1, 3)
            langues_choisies = random.sample(langues_ids, min(nb_langues, len(langues_ids)))
            for langue_id in langues_choisies:
                niveau = random.choice(['Débutant', 'Intermédiaire', 'Avancé', 'Courant'])
                cursor.execute("""
                    INSERT INTO personnel_langue_original 
                    (matricule, langue_id, niveau)
                    VALUES (%s, %s, %s)
                """, (matricule, langue_id, niveau))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n✅ Tables originales peuplées avec succès !")
        print("Les fiches de renseignement devraient maintenant s'afficher correctement.")
        return True
        
    except Error as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    print("🎯 PEUPLEMENT DES TABLES ORIGINALES DU CHECKPOINT 70")
    print("=" * 60)
    
    success = populate_original_tables()
    
    if success:
        print("\n🎉 Données de test créées avec succès !")
        print("Vous pouvez maintenant tester les fiches de renseignement.")
    else:
        print("\n❌ Échec du peuplement des données")
