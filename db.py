from flask_sqlalchemy import SQLAlchemy
from flask import Flask
from sqlalchemy import or_
from datetime import datetime

db = SQLAlchemy()

def init_app(app):
    # Configuration de la base de données MySQL
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+mysqldb://root:@localhost/gestion_vehicules'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Initialisation de l'extension
    db.init_app(app)

    # Création de toutes les tables
    with app.app_context():
        db.create_all()
        print("Base de données MySQL initialisée avec succès !")

class VehiculeGAR(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    matricule = db.Column(db.String(50), nullable=False, unique=True)
    unite = db.Column(db.String(20), nullable=True)
    type_vehicule = db.Column(db.String(20), nullable=False)
    marque = db.Column(db.String(50), nullable=False)

    # Relation avec les entretiens
    entretiens = db.relationship('Entretien', backref='vehicule', lazy=True)

    def __repr__(self):
        return f'<VehiculeGAR {self.matricule} - {self.unite}>'

class Entretien(db.Model):
    __tablename__ = 'entretien'
    
    id = db.Column(db.Integer, primary_key=True)
    vehicule_id = db.Column(db.Integer, db.ForeignKey('vehicule_gar.id'), nullable=False)
    type_entretien = db.Column(db.String(50), nullable=False)
    date_entretien = db.Column(db.DateTime, nullable=False)
    kilometrage = db.Column(db.Integer, nullable=False)
    kilometrage_prochain = db.Column(db.Integer, nullable=True)
    description = db.Column(db.Text, nullable=True)
    pieces_remplacees = db.Column(db.Text, nullable=True)
    prochain_entretien = db.Column(db.DateTime, nullable=True)
    statut = db.Column(db.String(20), nullable=False, default='Planifié')

    def __repr__(self):
        return f'<Entretien {self.id} - {self.type_entretien}>'

# ===================================================================
# MODÈLES POUR LA GESTION DES COURRIERS
# ===================================================================

class CourrierArrive(db.Model):
    __tablename__ = 'courrier_arrive'

    id = db.Column(db.Integer, primary_key=True)
    id_courrier = db.Column(db.String(50), nullable=False, unique=True)  # ID unique du courrier
    urgence = db.Column(db.Enum('extreme', 'extreme_urgent', 'urgent', 'routine', name='urgence_enum'), nullable=False)
    nature = db.Column(db.Enum('message', 'nds', 'note_royale', 'decision', name='nature_enum'), nullable=False)
    date_arrivee = db.Column(db.Date, nullable=False)
    date_signature = db.Column(db.Date, nullable=True)
    numero_ecrit = db.Column(db.String(100), nullable=False)
    expediteur = db.Column(db.String(200), nullable=False)
    objet = db.Column(db.Text, nullable=False)
    classification = db.Column(db.Enum('public', 'restreint', 'confidentiel', 'secret', name='classification_enum'), nullable=True)
    annotation = db.Column(db.Text, nullable=True)
    date_creation = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    # Relations avec les divisions
    divisions_action = db.relationship('CourrierDivisionAction', backref='courrier_arrive', lazy=True, cascade="all, delete-orphan")
    divisions_info = db.relationship('CourrierDivisionInfo', backref='courrier_arrive', lazy=True, cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id_courrier,
            'urgence': self.urgence,
            'nature': self.nature,
            'date_arrivee': self.date_arrivee.strftime('%Y-%m-%d'),
            'date_signature': self.date_signature.strftime('%Y-%m-%d') if self.date_signature else None,
            'numero_ecrit': self.numero_ecrit,
            'expediteur': self.expediteur,
            'objet': self.objet,
            'classification': self.classification,
            'annotation': self.annotation,
            'divisions_action': [div.division for div in self.divisions_action],
            'divisions_info': [div.division for div in self.divisions_info]
        }

class CourrierEnvoye(db.Model):
    __tablename__ = 'courrier_envoye'

    id = db.Column(db.Integer, primary_key=True)
    id_courrier = db.Column(db.String(50), nullable=False, unique=True)  # ID unique du courrier
    numero_ecrit = db.Column(db.String(100), nullable=False, unique=True)
    division_emettrice = db.Column(db.Enum('courrier', 'instruction', 'technique', 'rh', 'mcpo', 'informatique', 'planification', 'asa', name='division_enum'), nullable=False)
    date_depart = db.Column(db.Date, nullable=False)
    nature = db.Column(db.Enum('message', 'nds', 'note_royale', 'decision', name='nature_enum'), nullable=False)
    objet = db.Column(db.Text, nullable=False)
    destinataire = db.Column(db.String(200), nullable=False)
    observations = db.Column(db.Text, nullable=True)
    date_creation = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id_courrier,
            'numero_ecrit': self.numero_ecrit,
            'division_emettrice': self.division_emettrice,
            'date_depart': self.date_depart.strftime('%Y-%m-%d'),
            'nature': self.nature,
            'objet': self.objet,
            'destinataire': self.destinataire,
            'observations': self.observations
        }

class CourrierDivisionAction(db.Model):
    __tablename__ = 'courrier_division_action'

    id = db.Column(db.Integer, primary_key=True)
    courrier_id = db.Column(db.Integer, db.ForeignKey('courrier_arrive.id'), nullable=False)
    division = db.Column(db.Enum('instruction', 'technique', 'rh', 'mcpo', 'informatique', 'planification', 'asa', name='division_enum'), nullable=False)

class CourrierDivisionInfo(db.Model):
    __tablename__ = 'courrier_division_info'

    id = db.Column(db.Integer, primary_key=True)
    courrier_id = db.Column(db.Integer, db.ForeignKey('courrier_arrive.id'), nullable=False)
    division = db.Column(db.Enum('instruction', 'technique', 'rh', 'mcpo', 'informatique', 'planification', 'asa', name='division_enum'), nullable=False)

# Nouveau modèle centralisé pour les courriers (optionnel - pour une future migration)
class Courrier(db.Model):
    __tablename__ = 'courrier'
    id = db.Column(db.Integer, primary_key=True)
    reference = db.Column(db.String(100), nullable=False, unique=True)
    type_courrier = db.Column(db.Enum('arrive', 'depart', name='type_courrier_enum'), nullable=False)
    nature = db.Column(db.Enum('message', 'nds', 'note_royale', 'decision', name='nature_enum'), nullable=False)
    urgence = db.Column(db.Enum('extreme', 'extreme_urgent', 'urgent', 'routine', name='urgence_enum'), nullable=False)
    date_courrier = db.Column(db.Date, nullable=False)
    date_signature = db.Column(db.Date, nullable=True)
    expediteur = db.Column(db.String(200), nullable=True)
    destinataire_externe = db.Column(db.String(200), nullable=True)
    objet = db.Column(db.Text, nullable=False)
    classification = db.Column(db.Enum('public', 'restreint', 'confidentiel', 'secret', name='classification_enum'), nullable=True)
    annotation = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    statut = db.Column(db.String(50), nullable=False, default='nouveau')
    date_creation = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'reference': self.reference,
            'type_courrier': self.type_courrier,
            'nature': self.nature,
            'urgence': self.urgence,
            'date_courrier': self.date_courrier.strftime('%Y-%m-%d'),
            'date_signature': self.date_signature.strftime('%Y-%m-%d') if self.date_signature else None,
            'expediteur': self.expediteur,
            'destinataire_externe': self.destinataire_externe,
            'objet': self.objet,
            'classification': self.classification,
            'annotation': self.annotation,
            'observations': self.observations,
            'statut': self.statut
        }
