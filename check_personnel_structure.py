#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vérification de la structure de la table personnel
"""

import mysql.connector
from mysql.connector import Error

def check_personnel_structure():
    """Vérifie la structure de la table personnel"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("🔍 STRUCTURE DE LA TABLE PERSONNEL")
        print("=" * 50)
        
        # Vérifier la structure de la table personnel
        cursor.execute("DESCRIBE personnel")
        columns = cursor.fetchall()
        
        print("Colonnes de la table personnel :")
        for column in columns:
            field, type_col, null, key, default, extra = column
            print(f"  - {field}: {type_col} {'NULL' if null == 'YES' else 'NOT NULL'} {key} {extra}")
        
        print(f"\nTotal: {len(columns)} colonnes")
        
        # Vérifier quelques enregistrements
        cursor.execute("SELECT * FROM personnel LIMIT 3")
        records = cursor.fetchall()
        
        print(f"\n📊 Échantillon de données ({len(records)} enregistrements) :")
        for i, record in enumerate(records, 1):
            print(f"  Enregistrement {i}: {record[:5]}...")  # Afficher les 5 premiers champs
        
        cursor.close()
        connection.close()
        
        return True
        
    except Error as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    check_personnel_structure()
