#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer les tables restantes (associatives et métiers)
"""

import mysql.connector
from mysql.connector import Error

def create_remaining_tables():
    """Crée les tables associatives et métiers"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("Connexion à la base de données réussie")
        
        # Désactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0;")
        
        # Tables à créer
        tables = [
            # Table associative personnel_langue
            ("personnel_langue", """
                CREATE TABLE personnel_langue (
                    personnel_matricule VARCHAR(20) NOT NULL,
                    langue_id INT NOT NULL,
                    PRIMARY KEY (personnel_matricule, langue_id),
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                    FOREIGN KEY (langue_id) REFERENCES referentiel_langue(id_langue) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            # Table historique_grade
            ("historique_grade", """
                CREATE TABLE historique_grade (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    grade_id INT NOT NULL,
                    date_effet DATE NOT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                    FOREIGN KEY (grade_id) REFERENCES referentiel_grade(id_grade)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            # Tables familiales
            ("conjoint", """
                CREATE TABLE conjoint (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    nom VARCHAR(80) NOT NULL,
                    prenom VARCHAR(80) NOT NULL,
                    nom_ar VARCHAR(80) NOT NULL,
                    prenom_ar VARCHAR(80) NOT NULL,
                    date_naissance DATE NOT NULL,
                    lieu_naissance VARCHAR(100) NOT NULL,
                    lieu_naissance_ar VARCHAR(100) NOT NULL,
                    adresse VARCHAR(200) NOT NULL,
                    adresse_ar VARCHAR(200) NOT NULL,
                    date_mariage DATE NOT NULL,
                    lieu_mariage VARCHAR(100) NOT NULL,
                    profession VARCHAR(100) NOT NULL,
                    profession_ar VARCHAR(100) NOT NULL,
                    cin_numero VARCHAR(20) NOT NULL,
                    gsm VARCHAR(20) NOT NULL,
                    nom_pere VARCHAR(80) NOT NULL,
                    prenom_pere VARCHAR(80) NOT NULL,
                    nom_pere_ar VARCHAR(80) NOT NULL,
                    prenom_pere_ar VARCHAR(80) NOT NULL,
                    nom_mere VARCHAR(80) NOT NULL,
                    prenom_mere VARCHAR(80) NOT NULL,
                    nom_mere_ar VARCHAR(80) NOT NULL,
                    prenom_mere_ar VARCHAR(80) NOT NULL,
                    profession_pere VARCHAR(100) NOT NULL,
                    profession_mere VARCHAR(100) NOT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("enfant", """
                CREATE TABLE enfant (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    conjoint_id INT NOT NULL,
                    nom VARCHAR(80) NOT NULL,
                    prenom VARCHAR(80) NOT NULL,
                    sexe_id INT NOT NULL,
                    date_naissance DATE NOT NULL,
                    lieu_naissance VARCHAR(100) NOT NULL,
                    date_deces DATE NULL,
                    FOREIGN KEY (conjoint_id) REFERENCES conjoint(id) ON DELETE CASCADE,
                    FOREIGN KEY (sexe_id) REFERENCES referentiel_genre(id_genre)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            # Tables médicales
            ("situation_medicale", """
                CREATE TABLE situation_medicale (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    maladies TEXT NOT NULL,
                    date_hospitalisation DATE NOT NULL,
                    lieu_hospitalisation VARCHAR(100) NOT NULL,
                    aptitude ENUM('apte', 'innapte') NOT NULL,
                    observations TEXT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("vaccination", """
                CREATE TABLE vaccination (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    situation_medicale_id INT NOT NULL,
                    date_vaccination DATE NOT NULL,
                    objet VARCHAR(100) NOT NULL,
                    observations TEXT NULL,
                    FOREIGN KEY (situation_medicale_id) REFERENCES situation_medicale(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("ptc", """
                CREATE TABLE ptc (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    situation_medicale_id INT NOT NULL,
                    date_ptc DATE NOT NULL,
                    duree INT NOT NULL COMMENT 'en jours',
                    date_debut DATE NOT NULL,
                    date_fin DATE NOT NULL,
                    objet VARCHAR(100) NOT NULL,
                    observations TEXT NULL,
                    FOREIGN KEY (situation_medicale_id) REFERENCES situation_medicale(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            # Tables d'absences
            ("absence_desertion", """
                CREATE TABLE absence_desertion (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    date_absence DATE NOT NULL,
                    date_desertion DATE NOT NULL,
                    date_retour DATE NOT NULL,
                    date_arret_solde DATE NOT NULL,
                    date_prise_solde DATE NOT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("absence_detachement", """
                CREATE TABLE absence_detachement (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    date_debut DATE NOT NULL,
                    adresse VARCHAR(200) NOT NULL,
                    pays VARCHAR(100) NOT NULL,
                    date_fin DATE NOT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("absence_permission", """
                CREATE TABLE absence_permission (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    date_debut DATE NOT NULL,
                    date_fin DATE NOT NULL,
                    adresse VARCHAR(200) NOT NULL,
                    numero_serie VARCHAR(50) NOT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            # Tables de mouvements
            ("mouvement_interbie", """
                CREATE TABLE mouvement_interbie (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    unite_precedente_id INT NOT NULL,
                    fonction VARCHAR(100) NOT NULL,
                    date_debut DATE NOT NULL,
                    date_fin DATE NOT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                    FOREIGN KEY (unite_precedente_id) REFERENCES referentiel_unite(id_unite)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("sejour_ops", """
                CREATE TABLE sejour_ops (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    unite_id INT NOT NULL,
                    date_debut DATE NOT NULL,
                    date_fin DATE NOT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                    FOREIGN KEY (unite_id) REFERENCES referentiel_unite(id_unite)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("liberation", """
                CREATE TABLE liberation (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    personnel_matricule VARCHAR(20) NOT NULL,
                    motif VARCHAR(200) NOT NULL,
                    date_liberation DATE NOT NULL,
                    observations TEXT NULL,
                    FOREIGN KEY (personnel_matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """)
        ]
        
        # Créer chaque table
        for i, (table_name, sql) in enumerate(tables, 12):  # Commence à 12 car 11 tables de référence + 1 personnel
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table_name};")
                cursor.execute(sql)
                print(f"✓ Table {i}/25 créée : {table_name}")
            except Error as e:
                print(f"✗ Erreur table {table_name}: {e}")
        
        # Valider les changements
        connection.commit()
        print("✓ Toutes les tables restantes créées avec succès")
        
        # Réactiver les contraintes
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1;")
        
    except Error as e:
        print(f"Erreur : {e}")
        return False
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("Connexion fermée")
    
    return True

if __name__ == "__main__":
    print("=== CRÉATION TABLES RESTANTES ===")
    print("Tables associatives et métiers")
    print("-" * 40)
    
    success = create_remaining_tables()
    
    if success:
        print("\n🎉 Toutes les tables restantes créées avec succès !")
        print("Les 25 tables RH sont maintenant prêtes.")
    else:
        print("\n❌ Échec de la création")
