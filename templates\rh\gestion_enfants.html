{% extends "rh/base_rh.html" %}

{% block title %}Gestion Enfants - {{ militaire.nom_complet }}{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-child"></i>
                                Gestion des Enfants
                            </h2>
                            <small style="color: var(--text-light);">
                                Militaire : {{ militaire.nom_complet }} ({{ militaire.matricule }})
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <a href="{{ url_for('rh.nouveau_enfant', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-plus"></i> Nouvel Enfant
                                </a>
                                <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques des Enfants -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-child stat-icon"></i>
                <div class="stat-number">{{ enfants|length }}</div>
                <div class="stat-label">Total Enfants</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-heart stat-icon text-success"></i>
                <div class="stat-number">{{ enfants|selectattr('est_vivant')|list|length }}</div>
                <div class="stat-label">Vivants</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-mars stat-icon text-primary"></i>
                <div class="stat-number">{{ enfants|selectattr('genre.libelle', 'equalto', 'Masculin')|list|length }}</div>
                <div class="stat-label">Garçons</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-venus stat-icon" style="color: #e91e63;"></i>
                <div class="stat-number">{{ enfants|selectattr('genre.libelle', 'equalto', 'Féminin')|list|length }}</div>
                <div class="stat-label">Filles</div>
            </div>
        </div>
    </div>

    <!-- Liste des Enfants -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                Liste des Enfants ({{ enfants|length }})
                            </h5>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouveau_enfant', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                <i class="fas fa-plus"></i> Ajouter un Enfant
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if enfants %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Nom Complet</th>
                                    <th>Genre</th>
                                    <th>Date Naissance</th>
                                    <th>Âge</th>
                                    <th>Lieu Naissance</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enfant in enfants %}
                                <tr class="table-row-hover">
                                    <td>
                                        <div>
                                            <strong>{{ enfant.nom }} {{ enfant.prenom }}</strong>
                                            <br><small class="text-muted" dir="rtl">{{ enfant.nom_ar }} {{ enfant.prenom_ar }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if enfant.genre %}
                                        <span class="badge bg-{{ 'primary' if enfant.genre.libelle == 'Masculin' else 'danger' }}">
                                            <i class="fas fa-{{ 'mars' if enfant.genre.libelle == 'Masculin' else 'venus' }}"></i>
                                            {{ enfant.genre.libelle }}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ enfant.date_naissance.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        <strong>{{ enfant.age }}</strong> ans
                                    </td>
                                    <td>{{ enfant.lieu_naissance }}</td>
                                    <td>
                                        {% if enfant.est_vivant %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-heart"></i> Vivant
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-cross"></i> Décédé
                                        </span>
                                        <br><small class="text-muted">{{ enfant.date_deces.strftime('%d/%m/%Y') if enfant.date_deces else '' }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('rh.modifier_enfant', enfant_id=enfant.id) }}" 
                                               class="btn btn-warning btn-sm" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-danger btn-sm" 
                                                    onclick="confirmerSuppression({{ enfant.id }}, '{{ enfant.nom }} {{ enfant.prenom }}')" 
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-child fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun enfant enregistré</h5>
                        <p class="text-muted">Cliquez sur "Ajouter un Enfant" pour commencer.</p>
                        <a href="{{ url_for('rh.nouveau_enfant', matricule=militaire.matricule) }}" class="btn btn-success-military">
                            <i class="fas fa-plus"></i> Ajouter le Premier Enfant
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Informations Complémentaires -->
    {% if enfants %}
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i>
                        Répartition par Âge
                    </h5>
                </div>
                <div class="card-body">
                    {% set enfants_par_age = {} %}
                    {% for enfant in enfants %}
                        {% set age_groupe = 'Moins de 5 ans' if enfant.age < 5 else '5-10 ans' if enfant.age < 11 else '11-15 ans' if enfant.age < 16 else '16-20 ans' if enfant.age < 21 else 'Plus de 20 ans' %}
                        {% if enfants_par_age.update({age_groupe: enfants_par_age.get(age_groupe, 0) + 1}) %}{% endif %}
                    {% endfor %}
                    
                    {% for groupe, count in enfants_par_age.items() %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-bold">{{ groupe }}</span>
                            <span class="badge badge-military">{{ count }}</span>
                        </div>
                        <div class="progress mt-2" style="height: 8px;">
                            <div class="progress-bar bg-info" style="width: {{ (count / enfants|length * 100) }}%"></div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-birthday-cake"></i>
                        Prochains Anniversaires
                    </h5>
                </div>
                <div class="card-body">
                    {% set prochains_anniversaires = [] %}
                    {% for enfant in enfants if enfant.est_vivant %}
                        {% set today = moment().date() %}
                        {% set birthday_this_year = enfant.date_naissance.replace(year=today.year) %}
                        {% set birthday_next = birthday_this_year if birthday_this_year >= today else birthday_this_year.replace(year=today.year + 1) %}
                        {% set jours_restants = (birthday_next - today).days %}
                        {% if prochains_anniversaires.append({'enfant': enfant, 'jours': jours_restants, 'date': birthday_next}) %}{% endif %}
                    {% endfor %}
                    
                    {% if prochains_anniversaires %}
                    {% for anniv in prochains_anniversaires|sort(attribute='jours')[:5] %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ anniv.enfant.nom }} {{ anniv.enfant.prenom }}</strong>
                            <br><small class="text-muted">{{ anniv.date.strftime('%d/%m') }}</small>
                        </div>
                        <span class="badge bg-{{ 'warning' if anniv.jours <= 30 else 'info' }}">
                            {% if anniv.jours == 0 %}
                            Aujourd'hui !
                            {% elif anniv.jours == 1 %}
                            Demain
                            {% else %}
                            {{ anniv.jours }} jours
                            {% endif %}
                        </span>
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted text-center">Aucun anniversaire à venir</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal de Confirmation de Suppression -->
<div class="modal fade" id="confirmationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la Suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'enfant <strong id="nomEnfant"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="formSuppression" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Supprimer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.table-row-hover {
    transition: all 0.3s ease;
}

.table-row-hover:hover {
    background-color: rgba(212, 175, 55, 0.1) !important;
    transform: scale(1.01);
}

.badge {
    font-size: 0.75em;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.progress {
    background-color: rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmerSuppression(enfantId, nomEnfant) {
    document.getElementById('nomEnfant').textContent = nomEnfant;
    document.getElementById('formSuppression').action = '/enfant/' + enfantId + '/supprimer';
    
    const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    modal.show();
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animation des statistiques
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 20;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 50);
    });
});
</script>
{% endblock %}
