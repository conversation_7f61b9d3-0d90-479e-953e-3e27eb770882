"""
Script de suppression des données de test RH
Supprime toutes les données de test générées tout en préservant les données de référence
Division des Ressources Humaines - Inspection de l'Artillerie
Forces Armées Royales
"""

from app import app
from db import db
from gestion_vehicules.rh.models import *

def supprimer_donnees_test():
    """Supprime toutes les données de test tout en préservant les référentiels"""
    
    try:
        with app.app_context():
            print("🗑️ SUPPRESSION DES DONNÉES DE TEST RH")
            print("=" * 60)
            
            # Compteurs pour le rapport
            compteurs = {
                'personnel': 0,
                'situations_medicales': 0,
                'hospitalisations': 0,
                'vaccinations': 0,
                'ptcs': 0,
                'conjoints': 0,
                'enfants': 0,
                'permissions': 0,
                'desertions': 0,
                'detachements': 0,
                'mutations': 0,
                'sejours_ops': 0,
                'liberations': 0,
                'sanctions': 0,
                'historique_grades': 0,
                'langues_personnel': 0
            }
            
            print("📊 Suppression des données métiers...")
            
            # 1. Suppression des langues du personnel
            langues_personnel = PersonnelLangue.query.all()
            for langue in langues_personnel:
                db.session.delete(langue)
                compteurs['langues_personnel'] += 1
            
            # 2. Suppression de l'historique des grades
            historique_grades = HistoriqueGrade.query.all()
            for historique in historique_grades:
                db.session.delete(historique)
                compteurs['historique_grades'] += 1
            
            # 3. Suppression des sanctions
            sanctions = Sanction.query.all()
            for sanction in sanctions:
                db.session.delete(sanction)
                compteurs['sanctions'] += 1
            
            # 4. Suppression des libérations
            liberations = Liberation.query.all()
            for liberation in liberations:
                db.session.delete(liberation)
                compteurs['liberations'] += 1
            
            # 5. Suppression des séjours opérationnels
            sejours_ops = SejourOperationnel.query.all()
            for sejour in sejours_ops:
                db.session.delete(sejour)
                compteurs['sejours_ops'] += 1
            
            # 6. Suppression des mutations
            mutations = MutationInterBie.query.all()
            for mutation in mutations:
                db.session.delete(mutation)
                compteurs['mutations'] += 1
            
            # 7. Suppression des détachements
            detachements = Detachement.query.all()
            for detachement in detachements:
                db.session.delete(detachement)
                compteurs['detachements'] += 1
            
            # 8. Suppression des désertions
            desertions = Desertion.query.all()
            for desertion in desertions:
                db.session.delete(desertion)
                compteurs['desertions'] += 1
            
            # 9. Suppression des permissions
            permissions = Permission.query.all()
            for permission in permissions:
                db.session.delete(permission)
                compteurs['permissions'] += 1
            
            # 10. Suppression des enfants
            enfants = Enfant.query.all()
            for enfant in enfants:
                db.session.delete(enfant)
                compteurs['enfants'] += 1
            
            # 11. Suppression des conjoints
            conjoints = Conjoint.query.all()
            for conjoint in conjoints:
                db.session.delete(conjoint)
                compteurs['conjoints'] += 1
            
            # 12. Suppression des PTC
            ptcs = PTC.query.all()
            for ptc in ptcs:
                db.session.delete(ptc)
                compteurs['ptcs'] += 1
            
            # 13. Suppression des vaccinations
            vaccinations = Vaccination.query.all()
            for vaccination in vaccinations:
                db.session.delete(vaccination)
                compteurs['vaccinations'] += 1
            
            # 14. Suppression des hospitalisations
            hospitalisations = Hospitalisation.query.all()
            for hospitalisation in hospitalisations:
                db.session.delete(hospitalisation)
                compteurs['hospitalisations'] += 1
            
            # 15. Suppression des situations médicales
            situations_medicales = SituationMedicale.query.all()
            for situation in situations_medicales:
                db.session.delete(situation)
                compteurs['situations_medicales'] += 1
            
            # 16. Suppression du personnel (en dernier car clé étrangère)
            personnel = Personnel.query.all()
            for militaire in personnel:
                db.session.delete(militaire)
                compteurs['personnel'] += 1
            
            # Commit de toutes les suppressions
            db.session.commit()
            
            print("✅ Suppression terminée avec succès!")
            print("\n📊 RAPPORT DE SUPPRESSION:")
            print("=" * 60)
            
            total_supprime = 0
            for table, count in compteurs.items():
                if count > 0:
                    print(f"   • {table.replace('_', ' ').title()}: {count} enregistrement(s)")
                    total_supprime += count
            
            print("=" * 60)
            print(f"🗑️ TOTAL SUPPRIMÉ: {total_supprime} enregistrements")
            print("\n✅ Les données de référence ont été préservées")
            print("📋 Tables de référence intactes:")
            print("   • Genres, Groupes sanguins, Catégories")
            print("   • Services, Spécialités, Unités, Grades")
            print("   • États matrimoniaux, Langues, Liens parenté")
            print("   • Types d'absence")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de la suppression: {str(e)}")
        try:
            db.session.rollback()
        except:
            pass
        return False

def confirmer_suppression():
    """Demande confirmation avant suppression"""
    print("⚠️ ATTENTION: Cette action va supprimer TOUTES les données de test!")
    print("📋 Seront supprimés:")
    print("   • Tous les militaires")
    print("   • Toutes les données familiales")
    print("   • Toutes les données médicales")
    print("   • Toutes les absences et mouvements")
    print("   • Tout l'historique")
    print("\n✅ Seront préservés:")
    print("   • Les tables de référence (grades, unités, etc.)")
    print("   • La structure de la base de données")
    
    while True:
        reponse = input("\n❓ Voulez-vous continuer? (oui/non): ").lower().strip()
        if reponse in ['oui', 'o', 'yes', 'y']:
            return True
        elif reponse in ['non', 'n', 'no']:
            return False
        else:
            print("⚠️ Veuillez répondre par 'oui' ou 'non'")

def verifier_donnees_apres_suppression():
    """Vérifie que les données ont bien été supprimées"""
    try:
        with app.app_context():
            print("\n🔍 VÉRIFICATION POST-SUPPRESSION:")
            print("=" * 40)
            
            # Vérification des tables principales
            tables_a_verifier = [
                (Personnel, "Personnel"),
                (SituationMedicale, "Situations médicales"),
                (Conjoint, "Conjoints"),
                (Enfant, "Enfants"),
                (Permission, "Permissions"),
                (HistoriqueGrade, "Historique grades")
            ]
            
            for model, nom in tables_a_verifier:
                count = model.query.count()
                status = "✅" if count == 0 else "❌"
                print(f"   {status} {nom}: {count} enregistrement(s)")
            
            # Vérification des référentiels (doivent être préservés)
            print("\n📋 RÉFÉRENTIELS PRÉSERVÉS:")
            referentiels = [
                (ReferentielGenre, "Genres"),
                (ReferentielGrade, "Grades"),
                (ReferentielUnite, "Unités"),
                (ReferentielService, "Services")
            ]
            
            for model, nom in referentiels:
                count = model.query.count()
                status = "✅" if count > 0 else "⚠️"
                print(f"   {status} {nom}: {count} enregistrement(s)")
            
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {str(e)}")

def main():
    """Fonction principale"""
    print("🗑️ SCRIPT DE SUPPRESSION DES DONNÉES DE TEST RH")
    print("Forces Armées Royales - Inspection de l'Artillerie")
    print("=" * 60)
    
    # Demander confirmation
    if not confirmer_suppression():
        print("\n❌ Suppression annulée par l'utilisateur")
        return
    
    print("\n🚀 Début de la suppression...")
    
    # Effectuer la suppression
    if supprimer_donnees_test():
        # Vérifier le résultat
        verifier_donnees_apres_suppression()
        
        print("\n🎉 SUPPRESSION TERMINÉE AVEC SUCCÈS!")
        print("🔗 Vous pouvez maintenant:")
        print("   • Générer de nouvelles données de test")
        print("   • Utiliser l'application avec une base vide")
        print("   • Accéder à l'application: http://localhost:3000/rh")
    else:
        print("\n❌ ÉCHEC DE LA SUPPRESSION")
        print("⚠️ Vérifiez les erreurs ci-dessus")

if __name__ == "__main__":
    main()
