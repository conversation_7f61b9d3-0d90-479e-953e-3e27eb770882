"""
Script d'application des spécifications exactes
GRADES : soldat 1°classe → colonel (14 grades uniquement)
UNITÉS : 1GAR → 26GAR, Inspection de l'Artillerie, etc. (format exact demandé)
"""

from app import app
from db import db
from gestion_vehicules.rh.models import ReferentielUnite, ReferentielGrade, Personnel

def supprimer_et_recreer_grades():
    """Supprime tous les grades et recrée exactement selon spécifications"""
    try:
        with app.app_context():
            print("🎖️ Suppression et recréation des grades...")
            
            # Mettre à jour le personnel avec un grade temporaire
            personnel = Personnel.query.all()
            for p in personnel:
                p.grade_actuel_id = None
            db.session.commit()
            
            # Supprimer tous les grades
            ReferentielGrade.query.delete()
            db.session.commit()
            
            # Créer exactement les 14 grades demandés
            grades_exacts = [
                ('SOL1', 'Soldat 1ère Classe', 1),
                ('SOL2', 'Soldat 2ème Classe', 2),
                ('BRG', 'Brigadier', 3),
                ('BRGC', 'Brigadier Chef', 4),
                ('MDL', 'MDL', 5),
                ('MDLC', 'MDL Chef', 6),
                ('ADJ', 'Adjudant', 7),
                ('ADJC', 'Adjudant Chef', 8),
                ('SLT', 'Sous-Lieutenant', 9),
                ('LTN', 'Lieutenant', 10),
                ('CPT', 'Capitaine', 11),
                ('CDT', 'Commandant', 12),
                ('LCL', 'Lieutenant-Colonel', 13),
                ('COL', 'Colonel', 14)
            ]
            
            for code_grade, libelle, niveau in grades_exacts:
                grade = ReferentielGrade(
                    code_grade=code_grade,
                    libelle=libelle,
                    niveau=niveau,
                    description=f"Grade: {libelle}"
                )
                db.session.add(grade)
            
            db.session.commit()
            
            # Réassigner des grades au personnel
            grades = ReferentielGrade.query.all()
            import random
            for p in personnel:
                p.grade_actuel_id = random.choice(grades).id_grade
            db.session.commit()
            
            print(f"✅ {len(grades_exacts)} grades créés exactement selon spécifications")
            
    except Exception as e:
        print(f"❌ Erreur grades: {str(e)}")
        db.session.rollback()

def supprimer_et_recreer_unites():
    """Supprime toutes les unités et recrée exactement selon spécifications"""
    try:
        with app.app_context():
            print("🏢 Suppression et recréation des unités...")
            
            # Mettre à jour le personnel avec une unité temporaire
            personnel = Personnel.query.all()
            for p in personnel:
                p.unite_id = None
            db.session.commit()
            
            # Supprimer toutes les unités
            ReferentielUnite.query.delete()
            db.session.commit()
            
            # Créer exactement les unités demandées dans l'ordre
            unites_exactes = []
            
            # 1GAR au 26GAR
            for i in range(1, 27):
                unites_exactes.append((f'{i}GAR', f'{i}GAR', 'Régiment'))
            
            # Unités spécialisées dans l'ordre exact
            unites_exactes.extend([
                ('INSPART', 'Inspection de l\'Artillerie', 'Inspection'),
                ('ERART', 'ERART', 'Autre'),
                ('GSA', 'GSA', 'Autre'),
                ('CFA', 'CFA', 'Autre'),
                ('1BUR', '1er Bureau', 'Bureau'),
                ('2BUR', '2ème Bureau', 'Bureau'),
                ('3BUR', '3ème Bureau', 'Bureau'),
                ('4BUR', '4ème Bureau', 'Bureau'),
                ('5BUR', '5ème Bureau', 'Bureau'),
                ('BREC', 'Bureau de Recrutement', 'Bureau'),
                ('BCOUR', 'Bureau Courrier', 'Bureau'),
                ('DPO', 'DPO', 'Autre'),
                ('PCA', 'PCA', 'Autre'),
                ('EMZS', 'État-Major Zone Sud', 'Autre'),
                ('EMZE', 'État-Major Zone Est', 'Autre'),
                ('SOPTAF', 'SOPTAF', 'Autre'),
                ('SOPSAG', 'SOPSAG', 'Autre'),
                ('SORIENT', 'S.ORIENTAL', 'Autre'),
                ('AUTRE', 'Autre', 'Autre')
            ])
            
            for code, libelle, type_unite in unites_exactes:
                unite = ReferentielUnite(
                    code=code,
                    libelle=libelle,
                    type_unite=type_unite
                )
                db.session.add(unite)
            
            db.session.commit()
            
            # Réassigner des unités au personnel
            unites = ReferentielUnite.query.all()
            import random
            for p in personnel:
                p.unite_id = random.choice(unites).id_unite
            db.session.commit()
            
            print(f"✅ {len(unites_exactes)} unités créées exactement selon spécifications")
            
    except Exception as e:
        print(f"❌ Erreur unités: {str(e)}")
        db.session.rollback()

def afficher_resultats():
    """Affiche les résultats finaux"""
    try:
        with app.app_context():
            print("\n📊 RÉSULTATS FINAUX:")
            print("=" * 60)
            
            # Grades dans l'ordre
            grades = ReferentielGrade.query.order_by(ReferentielGrade.niveau).all()
            print(f"🎖️ GRADES ({len(grades)}) - ORDRE EXACT:")
            for i, grade in enumerate(grades, 1):
                print(f"   {i:2d}. {grade.libelle}")
            
            # Unités dans l'ordre
            unites = ReferentielUnite.query.all()
            print(f"\n🏢 UNITÉS ({len(unites)}) - ORDRE EXACT:")
            
            # GAR
            gar_unites = [u for u in unites if 'GAR' in u.code and u.code != 'GAR']
            gar_unites.sort(key=lambda x: int(x.code.replace('GAR', '')))
            print("   📍 GAR:")
            for unite in gar_unites:
                print(f"      • {unite.libelle}")
            
            # Autres dans l'ordre d'ajout
            autres_unites = [u for u in unites if 'GAR' not in u.code or u.code == 'GAR']
            autres_codes_ordre = ['INSPART', 'ERART', 'GSA', 'CFA', '1BUR', '2BUR', '3BUR', '4BUR', '5BUR', 
                                 'BREC', 'BCOUR', 'DPO', 'PCA', 'EMZS', 'EMZE', 'SOPTAF', 'SOPSAG', 'SORIENT', 'AUTRE']
            
            print("   📍 Autres:")
            for code in autres_codes_ordre:
                unite = next((u for u in autres_unites if u.code == code), None)
                if unite:
                    print(f"      • {unite.libelle}")
            
            # Personnel
            total_personnel = Personnel.query.count()
            print(f"\n👥 PERSONNEL: {total_personnel} militaires")
            
    except Exception as e:
        print(f"❌ Erreur affichage: {str(e)}")

def main():
    """Fonction principale"""
    print("🎯 APPLICATION DES SPÉCIFICATIONS EXACTES")
    print("=" * 60)
    print("📋 GRADES (14 uniquement):")
    print("   Soldat 1ère classe → Colonel")
    print("📋 UNITÉS (format exact):")
    print("   1GAR → 26GAR, Inspection de l'Artillerie, etc.")
    print("=" * 60)
    
    print("\n🚀 Application des spécifications...")
    
    # Application exacte
    supprimer_et_recreer_grades()
    supprimer_et_recreer_unites()
    
    # Résultats
    afficher_resultats()
    
    print("\n🎉 SPÉCIFICATIONS APPLIQUÉES!")
    print("🔗 Testez maintenant:")
    print("   • Recherche: http://localhost:3000/rh/recherche")
    print("   • Nouveau militaire: http://localhost:3000/rh/nouveau_militaire")
    print("\n✅ Vous verrez EXACTEMENT les options demandées!")

if __name__ == "__main__":
    main()
