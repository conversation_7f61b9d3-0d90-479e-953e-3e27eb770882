{% extends "rh/base_rh.html" %}

{% block title %}Nouveau Courrier - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-envelope"></i>
                                Nouveau Courrier
                            </h2>
                            <small class="text-muted">Enregistrement d'un nouveau courrier d'arrivée ou de départ</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.liste_courriers') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à la Liste
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sélection du Type de Courrier -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-exchange-alt"></i>
                        Type de Courrier
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-check-military">
                                <input class="form-check-input" type="radio" name="type_courrier" id="arrivee" value="arrivee" checked>
                                <label class="form-check-label" for="arrivee">
                                    <i class="fas fa-inbox me-2"></i>
                                    <strong>Courrier d'Arrivée</strong>
                                    <br><small class="text-muted">Courrier reçu par le service RH</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-check-military">
                                <input class="form-check-input" type="radio" name="type_courrier" id="depart" value="depart">
                                <label class="form-check-label" for="depart">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    <strong>Courrier de Départ</strong>
                                    <br><small class="text-muted">Courrier envoyé par le service RH</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Création -->
    <form method="POST" class="needs-validation" novalidate>
        <input type="hidden" name="type_courrier" id="type_courrier_hidden" value="arrivee">
        
        <div class="row">
            <!-- Section 1: Informations de Base -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i>
                            Informations de Base
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Référence du Courrier *</label>
                            <input type="text" name="reference_courrier" class="form-control form-control-military" required
                                   placeholder="Ex: ARR-2024-001 ou DEP-2024-001">
                            <div class="invalid-feedback">La référence du courrier est obligatoire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Objet du Courrier *</label>
                            <textarea name="objet_courrier" class="form-control form-control-military" rows="3" required
                                      placeholder="Objet détaillé du courrier..."></textarea>
                            <div class="invalid-feedback">L'objet du courrier est obligatoire</div>
                        </div>
                        
                        <!-- Champ dynamique : Expéditeur/Destinataire -->
                        <div class="mb-3" id="expediteur_field">
                            <label class="form-label-military">Expéditeur *</label>
                            <input type="text" name="expediteur" class="form-control form-control-military" required
                                   placeholder="Ex: Ministère de la Défense">
                            <div class="invalid-feedback">L'expéditeur est obligatoire</div>
                        </div>
                        
                        <div class="mb-3" id="destinataire_field" style="display: none;">
                            <label class="form-label-military">Destinataire *</label>
                            <input type="text" name="destinataire" class="form-control form-control-military"
                                   placeholder="Ex: Commandement Régional">
                            <div class="invalid-feedback">Le destinataire est obligatoire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Priorité</label>
                            <select name="priorite" class="form-control form-control-military">
                                {% for priorite in priorites %}
                                <option value="{{ priorite }}" {% if priorite == 'Normale' %}selected{% endif %}>
                                    {{ priorite }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 2: Dates et Mode -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar"></i>
                            Dates et Mode
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Date du Courrier *</label>
                            <input type="date" name="date_courrier" class="form-control form-control-military" 
                                   value="{{ date.today() }}" required>
                            <div class="invalid-feedback">La date du courrier est obligatoire</div>
                        </div>
                        
                        <!-- Champ dynamique : Date réception/envoi -->
                        <div class="mb-3" id="date_reception_field">
                            <label class="form-label-military">Date de Réception *</label>
                            <input type="date" name="date_reception" class="form-control form-control-military" 
                                   value="{{ date.today() }}" required>
                            <div class="invalid-feedback">La date de réception est obligatoire</div>
                        </div>
                        
                        <div class="mb-3" id="date_envoi_field" style="display: none;">
                            <label class="form-label-military">Date d'Envoi *</label>
                            <input type="date" name="date_envoi" class="form-control form-control-military" 
                                   value="{{ date.today() }}">
                            <div class="invalid-feedback">La date d'envoi est obligatoire</div>
                        </div>
                        
                        <!-- Mode d'envoi (uniquement pour courriers de départ) -->
                        <div class="mb-3" id="mode_envoi_field" style="display: none;">
                            <label class="form-label-military">Mode d'Envoi</label>
                            <select name="mode_envoi" class="form-control form-control-military">
                                {% for mode in modes_envoi %}
                                <option value="{{ mode }}" {% if mode == 'Courrier' %}selected{% endif %}>
                                    {{ mode }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="alert alert-military">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="type_info">
                                <strong>Courrier d'Arrivée :</strong> Courrier reçu par le service RH
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Section 3: Observations -->
            <div class="col-lg-8 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note"></i>
                            Observations et Notes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Observations</label>
                            <textarea name="observations" class="form-control form-control-military" rows="4" 
                                      placeholder="Observations, notes de traitement, actions à entreprendre..."></textarea>
                        </div>
                        
                        <div class="alert alert-warning-military">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Rappel :</strong> Assurez-vous que toutes les informations sont correctes 
                            avant l'enregistrement. Le courrier sera ajouté au registre officiel.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 4: Validation -->
            <div class="col-lg-4 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-save"></i>
                            Validation
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success-military mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information :</strong> Ce courrier sera enregistré dans le système 
                            de gestion documentaire et sera traçable.
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success-military btn-lg">
                                <i class="fas fa-save"></i> Enregistrer le Courrier
                            </button>
                            <a href="{{ url_for('rh.liste_courriers') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                        </div>
                        
                        <hr class="my-3">
                        
                        <div class="text-center">
                            <h6 class="text-warning mb-2">Actions Rapides</h6>
                            <div class="d-grid gap-1">
                                <a href="{{ url_for('rh.dashboard') }}" class="btn btn-military btn-sm">
                                    <i class="fas fa-tachometer-alt"></i> Tableau de Bord
                                </a>
                                <a href="{{ url_for('rh.liste_courriers') }}" class="btn btn-info-military btn-sm">
                                    <i class="fas fa-envelope"></i> Liste Courriers
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-check-military .form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.form-check-military .form-check-label {
    cursor: pointer;
    padding: 15px;
    border: 2px solid transparent;
    border-radius: 10px;
    transition: all 0.3s ease;
    display: block;
    width: 100%;
}

.form-check-military .form-check-input:checked + .form-check-label {
    background: rgba(255, 213, 79, 0.1);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.form-control-military:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 213, 79, 0.25);
}

.invalid-feedback {
    color: var(--danger-color);
    font-weight: 600;
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-control:valid {
    border-color: var(--success-color);
}

.alert-warning-military {
    border-left: 4px solid var(--warning-color);
    background: rgba(255, 152, 0, 0.1);
    border-radius: 8px;
}

.alert-success-military {
    border-left: 4px solid var(--success-color);
    background: rgba(76, 175, 80, 0.1);
    border-radius: 8px;
}

.card-military {
    transition: all 0.3s ease;
}

.card-military:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 213, 79, 0.15);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Gestion du changement de type de courrier
document.querySelectorAll('input[name="type_courrier"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const type = this.value;
        document.getElementById('type_courrier_hidden').value = type;
        
        // Champs à afficher/masquer
        const expediteurField = document.getElementById('expediteur_field');
        const destinataireField = document.getElementById('destinataire_field');
        const dateReceptionField = document.getElementById('date_reception_field');
        const dateEnvoiField = document.getElementById('date_envoi_field');
        const modeEnvoiField = document.getElementById('mode_envoi_field');
        const typeInfo = document.getElementById('type_info');
        
        if (type === 'arrivee') {
            // Courrier d'arrivée
            expediteurField.style.display = 'block';
            destinataireField.style.display = 'none';
            dateReceptionField.style.display = 'block';
            dateEnvoiField.style.display = 'none';
            modeEnvoiField.style.display = 'none';
            
            // Mettre les champs requis
            document.querySelector('input[name="expediteur"]').required = true;
            document.querySelector('input[name="destinataire"]').required = false;
            document.querySelector('input[name="date_reception"]').required = true;
            document.querySelector('input[name="date_envoi"]').required = false;
            
            typeInfo.innerHTML = '<strong>Courrier d\'Arrivée :</strong> Courrier reçu par le service RH';
            
            // Générer référence automatique
            const today = new Date();
            const year = today.getFullYear();
            const refField = document.querySelector('input[name="reference_courrier"]');
            if (!refField.value) {
                refField.value = `ARR-${year}-001`;
            }
            
        } else {
            // Courrier de départ
            expediteurField.style.display = 'none';
            destinataireField.style.display = 'block';
            dateReceptionField.style.display = 'none';
            dateEnvoiField.style.display = 'block';
            modeEnvoiField.style.display = 'block';
            
            // Mettre les champs requis
            document.querySelector('input[name="expediteur"]').required = false;
            document.querySelector('input[name="destinataire"]').required = true;
            document.querySelector('input[name="date_reception"]').required = false;
            document.querySelector('input[name="date_envoi"]').required = true;
            
            typeInfo.innerHTML = '<strong>Courrier de Départ :</strong> Courrier envoyé par le service RH';
            
            // Générer référence automatique
            const today = new Date();
            const year = today.getFullYear();
            const refField = document.querySelector('input[name="reference_courrier"]');
            if (!refField.value || refField.value.startsWith('ARR-')) {
                refField.value = `DEP-${year}-001`;
            }
        }
    });
});

// Validation du formulaire
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Validation en temps réel
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
    
})();

// Auto-génération de référence selon la priorité
document.querySelector('select[name="priorite"]').addEventListener('change', function() {
    const refField = document.querySelector('input[name="reference_courrier"]');
    const type = document.querySelector('input[name="type_courrier"]:checked').value;
    const today = new Date();
    const year = today.getFullYear();
    
    if (this.value === 'Très urgente') {
        const prefix = type === 'arrivee' ? 'URG-ARR' : 'URG-DEP';
        refField.value = `${prefix}-${year}-001`;
    }
});

// Animation des cartes
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card-military');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    if (this.checkValidity()) {
        const type = document.querySelector('input[name="type_courrier"]:checked').value;
        const typeText = type === 'arrivee' ? 'd\'arrivée' : 'de départ';
        const confirmation = confirm(`Êtes-vous sûr de vouloir enregistrer ce courrier ${typeText} ?`);
        if (!confirmation) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
