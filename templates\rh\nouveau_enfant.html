{% extends "rh/base_rh.html" %}

{% block title %}Nouvel Enfant - {{ militaire.nom_complet }}{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-plus"></i>
                                Nouvel Enfant
                            </h2>
                            <small style="color: var(--text-light);">
                                Parent : {{ militaire.nom_complet }} ({{ militaire.matricule }})
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.gestion_enfants', matricule=militaire.matricule) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire Nouvel Enfant -->
    <form method="POST" class="needs-validation" novalidate>
        <div class="row">
            <!-- Informations Personnelles -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations Personnelles
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom *</label>
                                <input type="text" name="nom" class="form-control" required>
                                <div class="invalid-feedback">Le nom est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom *</label>
                                <input type="text" name="prenom" class="form-control" required>
                                <div class="invalid-feedback">Le prénom est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Nom (Arabe) *</label>
                                <input type="text" name="nom_ar" class="form-control" required dir="rtl">
                                <div class="invalid-feedback">Le nom en arabe est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Prénom (Arabe) *</label>
                                <input type="text" name="prenom_ar" class="form-control" required dir="rtl">
                                <div class="invalid-feedback">Le prénom en arabe est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Genre *</label>
                                <select name="genre_id" class="form-control" required>
                                    <option value="">Sélectionner...</option>
                                    {% for genre in genres %}
                                    <option value="{{ genre.id_genre }}">{{ genre.libelle }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">Le genre est obligatoire</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date de Naissance *</label>
                                <input type="date" name="date_naissance" class="form-control" required id="dateNaissance">
                                <div class="invalid-feedback">La date de naissance est obligatoire</div>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label fw-bold">Lieu de Naissance *</label>
                                <input type="text" name="lieu_naissance" class="form-control" required>
                                <div class="invalid-feedback">Le lieu de naissance est obligatoire</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations Complémentaires -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i>
                            Informations Complémentaires
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="estDecede" onchange="toggleDeces()">
                                    <label class="form-check-label fw-bold" for="estDecede">
                                        Enfant décédé
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6" id="dateDeces" style="display: none;">
                                <label class="form-label fw-bold">Date de Décès</label>
                                <input type="date" name="date_deces" class="form-control" id="inputDateDeces">
                            </div>
                            <div class="col-md-6" id="lieuDeces" style="display: none;">
                                <label class="form-label fw-bold">Lieu de Décès</label>
                                <input type="text" name="lieu_deces" class="form-control">
                            </div>
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Information :</strong> L'âge sera calculé automatiquement à partir de la date de naissance.
                                </div>
                            </div>
                            <div class="col-md-12" id="ageDisplay" style="display: none;">
                                <div class="alert alert-success">
                                    <i class="fas fa-birthday-cake me-2"></i>
                                    <strong>Âge calculé :</strong> <span id="ageValue"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Boutons d'Action -->
        <div class="row">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-success-military btn-lg me-3">
                            <i class="fas fa-save"></i> Enregistrer l'Enfant
                        </button>
                        <a href="{{ url_for('rh.gestion_enfants', matricule=militaire.matricule) }}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Fonction pour afficher/masquer les champs de décès
function toggleDeces() {
    const checkbox = document.getElementById('estDecede');
    const dateDeces = document.getElementById('dateDeces');
    const lieuDeces = document.getElementById('lieuDeces');
    const inputDateDeces = document.getElementById('inputDateDeces');
    
    if (checkbox.checked) {
        dateDeces.style.display = 'block';
        lieuDeces.style.display = 'block';
        inputDateDeces.required = true;
    } else {
        dateDeces.style.display = 'none';
        lieuDeces.style.display = 'none';
        inputDateDeces.required = false;
        inputDateDeces.value = '';
        document.querySelector('input[name="lieu_deces"]').value = '';
    }
}

// Calcul automatique de l'âge
function calculateAge(birthDate, deathDate = null) {
    const today = deathDate || new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    
    return age;
}

// Affichage de l'âge en temps réel
document.getElementById('dateNaissance').addEventListener('change', function() {
    if (this.value) {
        const birthDate = new Date(this.value);
        const age = calculateAge(birthDate);
        
        document.getElementById('ageValue').textContent = age + ' ans';
        document.getElementById('ageDisplay').style.display = 'block';
        
        // Validation de la date (ne peut pas être dans le futur)
        const today = new Date();
        if (birthDate > today) {
            this.setCustomValidity('La date de naissance ne peut pas être dans le futur');
        } else {
            this.setCustomValidity('');
        }
    } else {
        document.getElementById('ageDisplay').style.display = 'none';
    }
});

// Validation de la date de décès
document.getElementById('inputDateDeces').addEventListener('change', function() {
    const birthDate = new Date(document.getElementById('dateNaissance').value);
    const deathDate = new Date(this.value);
    
    if (birthDate && deathDate) {
        if (deathDate <= birthDate) {
            this.setCustomValidity('La date de décès doit être postérieure à la date de naissance');
        } else {
            this.setCustomValidity('');
            
            // Recalculer l'âge au décès
            const age = calculateAge(birthDate, deathDate);
            document.getElementById('ageValue').textContent = age + ' ans (au décès)';
        }
    }
});

// Auto-complétion du nom de famille
document.addEventListener('DOMContentLoaded', function() {
    const nomParent = '{{ militaire.nom }}';
    const nomInput = document.querySelector('input[name="nom"]');
    
    // Pré-remplir avec le nom du parent
    if (nomInput && nomParent) {
        nomInput.value = nomParent;
    }
});
</script>
{% endblock %}
