#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du fonctionnement du checkpoint 70
Vérification que les modèles originaux fonctionnent
"""

from app import app
from db import db
from gestion_vehicules.rh.models import Personnel

def test_checkpoint_70():
    """Test du fonctionnement du checkpoint 70"""
    
    with app.app_context():
        try:
            print("🧪 TEST DU CHECKPOINT 70")
            print("=" * 50)
            
            # Test 1: Vérifier que les tables Personnel existent
            print("1. Test de la table Personnel...")
            personnel_count = Personnel.query.count()
            print(f"   ✓ {personnel_count} militaires trouvés")
            
            if personnel_count > 0:
                # Prendre le premier militaire
                militaire = Personnel.query.first()
                print(f"   ✓ Premier militaire: {militaire.nom} {militaire.prenom} ({militaire.matricule})")
                
                # Test 2: Vérifier l'accès aux modèles originaux
                print("\n2. Test des modèles originaux...")
                
                # Import des modèles originaux
                from gestion_vehicules.rh.models import (
                    SituationMedicale, Hospitalisation, Vaccination, PTC,
                    Conjoint, Enfant, Permission, Desertion, Detachement,
                    MutationInterBie, SejourOperationnel, Liberation, Sanction,
                    HistoriqueGrade, PersonnelLangue
                )
                
                print("   ✓ Tous les modèles originaux importés avec succès")
                
                # Test 3: Vérifier les tables
                print("\n3. Test des tables originales...")
                tables_test = [
                    ('SituationMedicale', SituationMedicale),
                    ('Hospitalisation', Hospitalisation),
                    ('Vaccination', Vaccination),
                    ('PTC', PTC),
                    ('Conjoint', Conjoint),
                    ('Enfant', Enfant),
                    ('Permission', Permission),
                    ('Desertion', Desertion),
                    ('Detachement', Detachement),
                    ('MutationInterBie', MutationInterBie),
                    ('SejourOperationnel', SejourOperationnel),
                    ('Liberation', Liberation),
                    ('Sanction', Sanction),
                    ('HistoriqueGrade', HistoriqueGrade),
                    ('PersonnelLangue', PersonnelLangue)
                ]
                
                for nom_table, modele in tables_test:
                    try:
                        count = modele.query.count()
                        print(f"   ✓ {nom_table}: {count} enregistrements")
                    except Exception as e:
                        print(f"   ✗ {nom_table}: Erreur - {e}")
                
                # Test 4: Test de la route fiche_personnel
                print("\n4. Test de la route fiche_personnel...")
                try:
                    with app.test_client() as client:
                        response = client.get(f'/rh/personnel/{militaire.matricule}')
                        if response.status_code == 200:
                            print(f"   ✓ Route fiche_personnel accessible (status: {response.status_code})")
                        else:
                            print(f"   ✗ Route fiche_personnel erreur (status: {response.status_code})")
                except Exception as e:
                    print(f"   ✗ Erreur route fiche_personnel: {e}")
                
                print("\n✅ CHECKPOINT 70 TESTÉ AVEC SUCCÈS !")
                print("L'application devrait maintenant fonctionner comme avant la reconstruction.")
                
            else:
                print("❌ Aucun personnel trouvé dans la base de données")
                
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_checkpoint_70()
