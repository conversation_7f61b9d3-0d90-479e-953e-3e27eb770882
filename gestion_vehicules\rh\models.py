#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèles SQLAlchemy pour le module RH - Version Checkpoint 70
Modèles originaux qui fonctionnaient avant la reconstruction
"""

from db import db
from datetime import datetime

# =============================================================================
# TABLES RÉFÉRENTIELLES
# =============================================================================

class ReferentielUnite(db.Model):
    __tablename__ = 'referentiel_unite'
    
    id_unite = db.Column(db.Integer, primary_key=True, autoincrement=True)
    code_unite = db.Column(db.String(20), nullable=False, unique=True)
    libelle = db.Column(db.String(100), nullable=False)
    type_unite = db.Column(db.Enum('Régiment', 'Inspection', 'Bureau', 'Autre'), nullable=False)

    def __repr__(self):
        return f'<Unite {self.libelle}>'

class ReferentielGrade(db.Model):
    __tablename__ = 'referentiel_grade'

    id_grade = db.Column(db.Integer, primary_key=True, autoincrement=True)
    code_grade = db.Column(db.String(20), nullable=False, unique=True)
    libelle = db.Column(db.String(50), nullable=False)
    ordre_hierarchique = db.Column(db.Integer, nullable=False)
    type_grade = db.Column(db.Enum('Officier', 'Sous-Officier', 'Militaire du rang'), nullable=False)

    def __repr__(self):
        return f'<Grade {self.libelle}>'

class ReferentielGenre(db.Model):
    __tablename__ = 'referentiel_genre'
    
    id_genre = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(20), nullable=False, unique=True)

    def __repr__(self):
        return f'<Genre {self.libelle}>'

class ReferentielTypeAbsence(db.Model):
    __tablename__ = 'referentiel_type_absence'
    
    id_type = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)

    def __repr__(self):
        return f'<TypeAbsence {self.libelle}>'

class ReferentielLangue(db.Model):
    __tablename__ = 'referentiel_langue'

    id_langue = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False, unique=True)
    code_iso = db.Column(db.String(5), nullable=True)

    def __repr__(self):
        return f'<Langue {self.libelle}>'

class ReferentielCategorie(db.Model):
    __tablename__ = 'referentiel_categorie'

    id_categorie = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)

    def __repr__(self):
        return f'<Categorie {self.libelle}>'

class ReferentielGroupeSanguin(db.Model):
    __tablename__ = 'referentiel_groupe_sanguin'

    id_groupe = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(10), nullable=False, unique=True)

    def __repr__(self):
        return f'<GroupeSanguin {self.libelle}>'

class ReferentielService(db.Model):
    __tablename__ = 'referentiel_service'

    id_service = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f'<Service {self.libelle}>'

class ReferentielSpecialite(db.Model):
    __tablename__ = 'referentiel_specialite'

    id_specialite = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f'<Specialite {self.libelle}>'

class ReferentielEtatMatrimonial(db.Model):
    __tablename__ = 'referentiel_etat_matrimonial'

    id_etat = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(30), nullable=False, unique=True)

    def __repr__(self):
        return f'<EtatMatrimonial {self.libelle}>'

class ReferentielLienParente(db.Model):
    __tablename__ = 'referentiel_lien_parente'

    id_lien = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False, unique=True)

    def __repr__(self):
        return f'<LienParente {self.libelle}>'

# =============================================================================
# TABLE PERSONNEL PRINCIPALE
# =============================================================================

class Personnel(db.Model):
    __tablename__ = 'personnel'

    matricule = db.Column(db.String(20), primary_key=True)
    nom = db.Column(db.String(80), nullable=False)
    prenom = db.Column(db.String(80), nullable=False)
    nom_ar = db.Column(db.String(80), nullable=False)
    prenom_ar = db.Column(db.String(80), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    genre_id = db.Column(db.Integer, nullable=False)
    categorie_id = db.Column(db.Integer, nullable=False)
    groupe_sanguin_id = db.Column(db.Integer, nullable=False)
    cin_numero = db.Column(db.String(20), nullable=False)
    cin_date_delivrance = db.Column(db.Date, nullable=False)
    cin_date_expiration = db.Column(db.Date, nullable=False)
    gsm = db.Column(db.String(20), nullable=False)
    telephone_domicile = db.Column(db.String(20), nullable=True)
    taille_cm = db.Column(db.Integer, nullable=False)
    lieu_residence = db.Column(db.String(200), nullable=False)
    service_id = db.Column(db.Integer, nullable=False)
    specialite_id = db.Column(db.Integer, nullable=True)
    unite_id = db.Column(db.Integer, nullable=False)
    grade_actuel_id = db.Column(db.Integer, nullable=False)
    fonction = db.Column(db.String(100), nullable=False)
    date_prise_fonction = db.Column(db.Date, nullable=False)
    ccp_numero = db.Column(db.String(50), nullable=False)
    compte_bancaire_numero = db.Column(db.String(50), nullable=True)
    somme_numero = db.Column(db.String(50), nullable=False)
    date_engagement = db.Column(db.Date, nullable=False)
    nom_pere = db.Column(db.String(80), nullable=False)
    prenom_pere = db.Column(db.String(80), nullable=False)
    nom_mere = db.Column(db.String(80), nullable=False)
    prenom_mere = db.Column(db.String(80), nullable=False)
    adresse_parents = db.Column(db.String(200), nullable=False)
    etat_matrimonial_id = db.Column(db.Integer, nullable=False)
    nombre_enfants = db.Column(db.Integer, nullable=True)
    passeport_numero = db.Column(db.String(20), nullable=True)
    passeport_date_delivrance = db.Column(db.Date, nullable=True)
    passeport_date_expiration = db.Column(db.Date, nullable=True)
    gsm_urgence = db.Column(db.String(20), nullable=False)
    lien_parente_id = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, nullable=False)
    updated_at = db.Column(db.DateTime, nullable=False)

    def __repr__(self):
        return f'<Personnel {self.matricule} - {self.prenom} {self.nom}>'

# =============================================================================
# MODÈLES ORIGINAUX DU CHECKPOINT 70
# =============================================================================

class SituationMedicale(db.Model):
    """Modèle original pour la situation médicale (checkpoint 70)"""
    __tablename__ = 'situation_medicale_original'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    aptitude = db.Column(db.Enum('Apte', 'Inapte'), nullable=False, default='Apte')
    date_derniere_visite = db.Column(db.Date, nullable=True)
    observations_generales = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<SituationMedicale {self.matricule}>'

class Hospitalisation(db.Model):
    """Modèle original pour les hospitalisations (checkpoint 70)"""
    __tablename__ = 'hospitalisation_original'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_entree = db.Column(db.Date, nullable=False)
    date_sortie = db.Column(db.Date, nullable=True)
    etablissement = db.Column(db.String(200), nullable=False)
    motif = db.Column(db.String(500), nullable=False)
    diagnostic = db.Column(db.Text, nullable=True)
    traitement = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Hospitalisation {self.matricule} - {self.date_entree}>'

class Vaccination(db.Model):
    """Modèle original pour les vaccinations (checkpoint 70)"""
    __tablename__ = 'vaccination_original'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    nom_vaccin = db.Column(db.String(100), nullable=False)
    date_vaccination = db.Column(db.Date, nullable=False)
    numero_lot = db.Column(db.String(50), nullable=True)
    lieu_vaccination = db.Column(db.String(200), nullable=True)
    medecin_vaccinateur = db.Column(db.String(100), nullable=True)
    rappel_prevu = db.Column(db.Date, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Vaccination {self.matricule} - {self.nom_vaccin}>'

class PTC(db.Model):
    """Modèle original pour les PTC (checkpoint 70)"""
    __tablename__ = 'ptc_original'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    duree_jours = db.Column(db.Integer, nullable=False)
    objet = db.Column(db.String(500), nullable=False)
    lieu_ptc = db.Column(db.String(200), nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<PTC {self.matricule} - {self.date_debut}>'

class Conjoint(db.Model):
    """Modèle original pour les conjoints (checkpoint 70)"""
    __tablename__ = 'conjoint_original'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    nom = db.Column(db.String(80), nullable=False)
    prenom = db.Column(db.String(80), nullable=False)
    nom_ar = db.Column(db.String(80), nullable=False)
    prenom_ar = db.Column(db.String(80), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    cin_numero = db.Column(db.String(20), nullable=False)
    cin_date_delivrance = db.Column(db.Date, nullable=False)
    cin_date_expiration = db.Column(db.Date, nullable=False)
    nom_pere = db.Column(db.String(80), nullable=False)
    prenom_pere = db.Column(db.String(80), nullable=False)
    nom_mere = db.Column(db.String(80), nullable=False)
    prenom_mere = db.Column(db.String(80), nullable=False)
    profession = db.Column(db.String(100), nullable=True)
    lieu_travail = db.Column(db.String(200), nullable=True)
    gsm = db.Column(db.String(20), nullable=True)
    date_mariage = db.Column(db.Date, nullable=False)
    lieu_mariage = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Conjoint {self.nom} {self.prenom}>'

class Enfant(db.Model):
    """Modèle original pour les enfants (checkpoint 70)"""
    __tablename__ = 'enfant_original'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    nom = db.Column(db.String(80), nullable=False)
    prenom = db.Column(db.String(80), nullable=False)
    nom_ar = db.Column(db.String(80), nullable=False)
    prenom_ar = db.Column(db.String(80), nullable=False)
    genre_id = db.Column(db.Integer, db.ForeignKey('referentiel_genre.id_genre'), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    date_deces = db.Column(db.Date, nullable=True)
    lieu_deces = db.Column(db.String(100), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    genre = db.relationship('ReferentielGenre', backref='enfants_original')

    def __repr__(self):
        return f'<Enfant {self.nom} {self.prenom}>'

class Permission(db.Model):
    """Modèle original pour les permissions (checkpoint 70)"""
    __tablename__ = 'permission_original'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    type_absence_id = db.Column(db.Integer, db.ForeignKey('referentiel_type_absence.id_type'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    duree_jours = db.Column(db.Integer, nullable=False)
    adresse_permission = db.Column(db.String(500), nullable=True)
    numero_serie = db.Column(db.String(50), nullable=True)
    motif = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    type_absence = db.relationship('ReferentielTypeAbsence', backref='permissions_original')

    def __repr__(self):
        return f'<Permission {self.matricule} - {self.date_debut}>'

class Desertion(db.Model):
    """Modèle original pour les désertions (checkpoint 70)"""
    __tablename__ = 'desertion_original'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_absence = db.Column(db.Date, nullable=False)
    date_retour = db.Column(db.Date, nullable=True)
    date_arret_solde = db.Column(db.Date, nullable=True)
    date_prise_solde = db.Column(db.Date, nullable=True)
    motif = db.Column(db.Text, nullable=True)
    circonstances = db.Column(db.Text, nullable=True)
    sanctions = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Desertion {self.matricule} - {self.date_absence}>'

class Detachement(db.Model):
    """Modèle original pour les détachements (checkpoint 70)"""
    __tablename__ = 'detachement_original'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=True)
    lieu_detachement = db.Column(db.String(200), nullable=False)
    adresse_detachement = db.Column(db.String(500), nullable=True)
    pays = db.Column(db.String(100), nullable=False, default='Maroc')
    organisme_accueil = db.Column(db.String(200), nullable=True)
    fonction_detachement = db.Column(db.String(200), nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Detachement {self.matricule} - {self.lieu_detachement}>'

class MutationInterBie(db.Model):
    """Modèle original pour les mutations (checkpoint 70)"""
    __tablename__ = 'mutation_inter_bie_original'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    unite_origine_id = db.Column(db.Integer, db.ForeignKey('referentiel_unite.id_unite'), nullable=False)
    unite_destination_id = db.Column(db.Integer, db.ForeignKey('referentiel_unite.id_unite'), nullable=False)
    fonction_origine = db.Column(db.String(200), nullable=False)
    fonction_destination = db.Column(db.String(200), nullable=False)
    date_mutation = db.Column(db.Date, nullable=False)
    numero_decision = db.Column(db.String(50), nullable=True)
    motif = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    unite_origine = db.relationship('ReferentielUnite', foreign_keys=[unite_origine_id], backref='mutations_origine_original')
    unite_destination = db.relationship('ReferentielUnite', foreign_keys=[unite_destination_id], backref='mutations_destination_original')

    def __repr__(self):
        return f'<MutationInterBie {self.matricule} - {self.date_mutation}>'

class SejourOperationnel(db.Model):
    """Modèle original pour les séjours opérationnels (checkpoint 70)"""
    __tablename__ = 'sejour_operationnel_original'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    unite_operationnelle = db.Column(db.String(200), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=True)
    lieu_operation = db.Column(db.String(200), nullable=False)
    type_operation = db.Column(db.String(100), nullable=True)
    fonction_operationnelle = db.Column(db.String(200), nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<SejourOperationnel {self.matricule} - {self.unite_operationnelle}>'

class Liberation(db.Model):
    """Modèle original pour les libérations (checkpoint 70)"""
    __tablename__ = 'liberation_original'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_liberation = db.Column(db.Date, nullable=False)
    motif_liberation = db.Column(db.String(200), nullable=False)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Liberation {self.matricule} - {self.date_liberation}>'

class Sanction(db.Model):
    """Modèle original pour les sanctions (checkpoint 70)"""
    __tablename__ = 'sanction_original'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    type_sanction = db.Column(db.String(100), nullable=False)
    date_sanction = db.Column(db.Date, nullable=False)
    duree_jours = db.Column(db.Integer, nullable=True)
    motif = db.Column(db.Text, nullable=False)
    autorite_sanctionnante = db.Column(db.String(200), nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    date_execution = db.Column(db.Date, nullable=True)
    date_fin_execution = db.Column(db.Date, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Sanction {self.matricule} - {self.type_sanction}>'

class HistoriqueGrade(db.Model):
    """Modèle original pour l'historique des grades (checkpoint 70)"""
    __tablename__ = 'historique_grade_original'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    grade_id = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    grade = db.relationship('ReferentielGrade', backref='historique_grades_original')

    def __repr__(self):
        return f'<HistoriqueGrade {self.matricule} - {self.grade.libelle if self.grade else ""}>'

class PersonnelLangue(db.Model):
    """Modèle original pour les langues du personnel (checkpoint 70)"""
    __tablename__ = 'personnel_langue_original'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    langue_id = db.Column(db.Integer, db.ForeignKey('referentiel_langue.id_langue'), nullable=False)
    niveau = db.Column(db.Enum('Débutant', 'Intermédiaire', 'Avancé', 'Courant'), nullable=False)
    date_ajout = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    langue = db.relationship('ReferentielLangue', backref='personnel_langues_original')

    def __repr__(self):
        return f'<PersonnelLangue {self.matricule} - {self.langue.libelle if self.langue else ""}>'
