"""
Modèles SQLAlchemy pour l'application RH militaire
Architecture basée sur architecture_rh_militaire.md - 25 tables au total
Division des Ressources Humaines - Inspection de l'Artillerie
Forces Armées Royales
"""

from db import db
from datetime import datetime, date

# =============================================================================
# 1. TABLES DE RÉFÉRENCE (DICTIONNAIRES DE VALEURS) - 11 tables
# =============================================================================

class ReferentielGenre(db.Model):
    """Table de référence : genres/sexes biologiques"""
    __tablename__ = 'referentiel_genre'
    
    id_genre = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(20), nullable=False, unique=True)
    
    def __repr__(self):
        return f'<Genre {self.libelle}>'

class ReferentielGroupeSanguin(db.Model):
    """Table de référence : groupes sanguins"""
    __tablename__ = 'referentiel_groupe_sanguin'
    
    id_groupe = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(3), nullable=False, unique=True)
    
    def __repr__(self):
        return f'<GroupeSanguin {self.libelle}>'

class ReferentielCategorie(db.Model):
    """Table de référence : catégories militaires"""
    __tablename__ = 'referentiel_categorie'
    
    id_categorie = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    
    def __repr__(self):
        return f'<Categorie {self.libelle}>'

class ReferentielService(db.Model):
    """Table de référence : armes et services"""
    __tablename__ = 'referentiel_service'
    
    id_service = db.Column(db.Integer, primary_key=True, autoincrement=True)
    code_court = db.Column(db.String(10), nullable=False, unique=True)
    libelle = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    def __repr__(self):
        return f'<Service {self.code_court} - {self.libelle}>'

class ReferentielSpecialite(db.Model):
    """Table de référence : spécialités rattachées à un service"""
    __tablename__ = 'referentiel_specialite'
    
    id_specialite = db.Column(db.Integer, primary_key=True, autoincrement=True)
    service_id = db.Column(db.Integer, db.ForeignKey('referentiel_service.id_service'), nullable=False)
    code = db.Column(db.String(20), nullable=False)
    libelle = db.Column(db.String(100), nullable=False)
    
    # Relations
    service = db.relationship('ReferentielService', backref='specialites')
    
    def __repr__(self):
        return f'<Specialite {self.code} - {self.libelle}>'

class ReferentielUnite(db.Model):
    """Table de référence : unités et bureaux"""
    __tablename__ = 'referentiel_unite'
    
    id_unite = db.Column(db.Integer, primary_key=True, autoincrement=True)
    code = db.Column(db.String(20), nullable=False, unique=True)
    libelle = db.Column(db.String(150), nullable=False)
    type_unite = db.Column(db.Enum('Régiment', 'Inspection', 'Bureau', 'Autre', name='type_unite_enum'), nullable=False)
    
    def __repr__(self):
        return f'<Unite {self.code} - {self.libelle}>'

class ReferentielGrade(db.Model):
    """Table de référence : échelle hiérarchique des grades"""
    __tablename__ = 'referentiel_grade'
    
    id_grade = db.Column(db.Integer, primary_key=True, autoincrement=True)
    code_grade = db.Column(db.String(20), nullable=False, unique=True)
    libelle = db.Column(db.String(50), nullable=False)
    niveau = db.Column(db.Integer, nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    def __repr__(self):
        return f'<Grade {self.code_grade} - {self.libelle} (Niveau {self.niveau})>'

class ReferentielEtatMatrimonial(db.Model):
    """Table de référence : états matrimoniaux"""
    __tablename__ = 'referentiel_etat_matrimonial'
    
    id_etat = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(20), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    
    def __repr__(self):
        return f'<EtatMatrimonial {self.libelle}>'

class ReferentielLangue(db.Model):
    """Table de référence : langues parlées"""
    __tablename__ = 'referentiel_langue'
    
    id_langue = db.Column(db.Integer, primary_key=True, autoincrement=True)
    code_iso = db.Column(db.String(2), nullable=False, unique=True)
    libelle = db.Column(db.String(50), nullable=False)
    
    def __repr__(self):
        return f'<Langue {self.code_iso} - {self.libelle}>'

class ReferentielLienParente(db.Model):
    """Table de référence : degrés de parenté pour contact d'urgence"""
    __tablename__ = 'referentiel_lien_parente'
    
    id_lien = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False, unique=True)
    
    def __repr__(self):
        return f'<LienParente {self.libelle}>'

class ReferentielTypeAbsence(db.Model):
    """Table de référence : catégories d'absence"""
    __tablename__ = 'referentiel_type_absence'
    
    id_type = db.Column(db.Integer, primary_key=True, autoincrement=True)
    libelle = db.Column(db.String(50), nullable=False, unique=True)
    
    def __repr__(self):
        return f'<TypeAbsence {self.libelle}>'

# =============================================================================
# 2. TABLE PRINCIPALE : PERSONNEL - 1 table
# =============================================================================

class Personnel(db.Model):
    """Table principale : informations civiles et militaires d'un soldat"""
    __tablename__ = 'personnel'
    
    # Clé primaire
    matricule = db.Column(db.String(20), primary_key=True)
    
    # Informations personnelles de base
    nom = db.Column(db.String(80), nullable=False)
    prenom = db.Column(db.String(80), nullable=False)
    nom_ar = db.Column(db.String(80), nullable=False)
    prenom_ar = db.Column(db.String(80), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    
    # Références vers tables de référence
    genre_id = db.Column(db.Integer, db.ForeignKey('referentiel_genre.id_genre'), nullable=False)
    categorie_id = db.Column(db.Integer, db.ForeignKey('referentiel_categorie.id_categorie'), nullable=False)
    groupe_sanguin_id = db.Column(db.Integer, db.ForeignKey('referentiel_groupe_sanguin.id_groupe'), nullable=False)
    
    # Documents d'identité
    cin_numero = db.Column(db.String(20), nullable=False)
    cin_date_delivrance = db.Column(db.Date, nullable=False)
    cin_date_expiration = db.Column(db.Date, nullable=False)
    
    # Coordonnées
    gsm = db.Column(db.String(20), nullable=False)
    telephone_domicile = db.Column(db.String(20), nullable=True)
    
    # Caractéristiques physiques
    taille_cm = db.Column(db.Integer, nullable=False)
    lieu_residence = db.Column(db.String(200), nullable=False)
    
    # Informations militaires
    service_id = db.Column(db.Integer, db.ForeignKey('referentiel_service.id_service'), nullable=False)
    specialite_id = db.Column(db.Integer, db.ForeignKey('referentiel_specialite.id_specialite'), nullable=True)
    unite_id = db.Column(db.Integer, db.ForeignKey('referentiel_unite.id_unite'), nullable=False)
    grade_actuel_id = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    fonction = db.Column(db.String(100), nullable=False)
    date_prise_fonction = db.Column(db.Date, nullable=False)
    date_engagement = db.Column(db.Date, nullable=False)
    
    # Informations financières
    ccp_numero = db.Column(db.String(50), nullable=False)
    compte_bancaire_numero = db.Column(db.String(50), nullable=True)
    somme_numero = db.Column(db.String(50), nullable=False)
    
    # Informations familiales
    nom_pere = db.Column(db.String(80), nullable=False)
    prenom_pere = db.Column(db.String(80), nullable=False)
    nom_mere = db.Column(db.String(80), nullable=False)
    prenom_mere = db.Column(db.String(80), nullable=False)
    adresse_parents = db.Column(db.String(200), nullable=False)
    
    # État matrimonial
    etat_matrimonial_id = db.Column(db.Integer, db.ForeignKey('referentiel_etat_matrimonial.id_etat'), nullable=False)
    nombre_enfants = db.Column(db.Integer, nullable=True)
    
    # Passeport (optionnel)
    passeport_numero = db.Column(db.String(20), nullable=True)
    passeport_date_delivrance = db.Column(db.Date, nullable=True)
    passeport_date_expiration = db.Column(db.Date, nullable=True)
    
    # Contact d'urgence
    gsm_urgence = db.Column(db.String(20), nullable=False)
    lien_parente_id = db.Column(db.Integer, db.ForeignKey('referentiel_lien_parente.id_lien'), nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    genre = db.relationship('ReferentielGenre', backref='personnel')
    categorie = db.relationship('ReferentielCategorie', backref='personnel')
    groupe_sanguin = db.relationship('ReferentielGroupeSanguin', backref='personnel')
    service = db.relationship('ReferentielService', backref='personnel')
    specialite = db.relationship('ReferentielSpecialite', backref='personnel')
    unite = db.relationship('ReferentielUnite', backref='personnel')
    grade_actuel = db.relationship('ReferentielGrade', backref='personnel')
    etat_matrimonial = db.relationship('ReferentielEtatMatrimonial', backref='personnel')
    lien_parente = db.relationship('ReferentielLienParente', backref='personnel')
    
    def __repr__(self):
        return f'<Personnel {self.matricule} - {self.grade_actuel.libelle if self.grade_actuel else ""} {self.nom} {self.prenom}>'
    
    @property
    def nom_complet(self):
        """Retourne le nom complet avec grade"""
        grade = self.grade_actuel.libelle if self.grade_actuel else ""
        return f"{grade} {self.nom} {self.prenom}".strip()
    
    @property
    def age(self):
        """Calcule l'âge actuel"""
        if self.date_naissance:
            today = date.today()
            return today.year - self.date_naissance.year - ((today.month, today.day) < (self.date_naissance.month, self.date_naissance.day))
        return None

# =============================================================================
# 3. TABLES ASSOCIATIVES ET MÉTIERS - 13 tables
# =============================================================================

class PersonnelLangue(db.Model):
    """Table associative : langues parlées par le personnel"""
    __tablename__ = 'personnel_langue'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    langue_id = db.Column(db.Integer, db.ForeignKey('referentiel_langue.id_langue'), nullable=False)
    niveau = db.Column(db.Enum('Débutant', 'Intermédiaire', 'Avancé', 'Courant', name='niveau_langue_enum'), nullable=False)
    date_ajout = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='langues')
    langue = db.relationship('ReferentielLangue', backref='personnel_langues')

    def __repr__(self):
        return f'<PersonnelLangue {self.matricule} - {self.langue.libelle if self.langue else ""} ({self.niveau})>'

class HistoriqueGrade(db.Model):
    """Table historique : évolution des grades dans le temps"""
    __tablename__ = 'historique_grade'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    grade_id = db.Column(db.Integer, db.ForeignKey('referentiel_grade.id_grade'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='historique_grades')
    grade = db.relationship('ReferentielGrade', backref='historique_grades')

    def __repr__(self):
        return f'<HistoriqueGrade {self.matricule} - {self.grade.libelle if self.grade else ""} ({self.date_debut})>'

class Conjoint(db.Model):
    """Table métier : informations sur le conjoint"""
    __tablename__ = 'conjoint'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    nom = db.Column(db.String(80), nullable=False)
    prenom = db.Column(db.String(80), nullable=False)
    nom_ar = db.Column(db.String(80), nullable=False)
    prenom_ar = db.Column(db.String(80), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    cin_numero = db.Column(db.String(20), nullable=False)
    cin_date_delivrance = db.Column(db.Date, nullable=False)
    cin_date_expiration = db.Column(db.Date, nullable=False)
    nom_pere = db.Column(db.String(80), nullable=False)
    prenom_pere = db.Column(db.String(80), nullable=False)
    nom_mere = db.Column(db.String(80), nullable=False)
    prenom_mere = db.Column(db.String(80), nullable=False)
    profession = db.Column(db.String(100), nullable=True)
    lieu_travail = db.Column(db.String(200), nullable=True)
    gsm = db.Column(db.String(20), nullable=True)
    date_mariage = db.Column(db.Date, nullable=False)
    lieu_mariage = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref=db.backref('conjoint', uselist=False))

    def __repr__(self):
        return f'<Conjoint {self.nom} {self.prenom} - Époux/se de {self.matricule}>'

class Enfant(db.Model):
    """Table métier : informations sur les enfants"""
    __tablename__ = 'enfant'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    nom = db.Column(db.String(80), nullable=False)
    prenom = db.Column(db.String(80), nullable=False)
    nom_ar = db.Column(db.String(80), nullable=False)
    prenom_ar = db.Column(db.String(80), nullable=False)
    genre_id = db.Column(db.Integer, db.ForeignKey('referentiel_genre.id_genre'), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    lieu_naissance = db.Column(db.String(100), nullable=False)
    date_deces = db.Column(db.Date, nullable=True)
    lieu_deces = db.Column(db.String(100), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='enfants')
    genre = db.relationship('ReferentielGenre', backref='enfants')

    def __repr__(self):
        return f'<Enfant {self.nom} {self.prenom} - Enfant de {self.matricule}>'

    @property
    def age(self):
        """Calcule l'âge actuel ou à la date de décès"""
        if self.date_naissance:
            end_date = self.date_deces if self.date_deces else date.today()
            return end_date.year - self.date_naissance.year - ((end_date.month, end_date.day) < (self.date_naissance.month, self.date_naissance.day))
        return None

    @property
    def est_vivant(self):
        """Indique si l'enfant est vivant"""
        return self.date_deces is None

class SituationMedicale(db.Model):
    """Table métier : situation médicale du personnel"""
    __tablename__ = 'situation_medicale'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    aptitude = db.Column(db.Enum('Apte', 'Inapte', name='aptitude_enum'), nullable=False, default='Apte')
    date_derniere_visite = db.Column(db.Date, nullable=True)
    observations_generales = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref=db.backref('situation_medicale', uselist=False))

    def __repr__(self):
        return f'<SituationMedicale {self.matricule} - {self.aptitude}>'

class Hospitalisation(db.Model):
    """Table métier : hospitalisations du personnel"""
    __tablename__ = 'hospitalisation'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_entree = db.Column(db.Date, nullable=False)
    date_sortie = db.Column(db.Date, nullable=True)
    etablissement = db.Column(db.String(200), nullable=False)
    motif = db.Column(db.String(500), nullable=False)
    diagnostic = db.Column(db.Text, nullable=True)
    traitement = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='hospitalisations')

    def __repr__(self):
        return f'<Hospitalisation {self.matricule} - {self.date_entree} ({self.motif[:50]}...)>'

    @property
    def duree_jours(self):
        """Calcule la durée d'hospitalisation en jours"""
        if self.date_entree:
            end_date = self.date_sortie if self.date_sortie else date.today()
            return (end_date - self.date_entree).days + 1
        return None

    @property
    def en_cours(self):
        """Indique si l'hospitalisation est en cours"""
        return self.date_sortie is None

class Vaccination(db.Model):
    """Table métier : vaccinations du personnel"""
    __tablename__ = 'vaccination'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    nom_vaccin = db.Column(db.String(100), nullable=False)
    date_vaccination = db.Column(db.Date, nullable=False)
    numero_lot = db.Column(db.String(50), nullable=True)
    lieu_vaccination = db.Column(db.String(200), nullable=True)
    medecin_vaccinateur = db.Column(db.String(100), nullable=True)
    rappel_prevu = db.Column(db.Date, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='vaccinations')

    def __repr__(self):
        return f'<Vaccination {self.matricule} - {self.nom_vaccin} ({self.date_vaccination})>'

class PTC(db.Model):
    """Table métier : Permissions Temporaires de Congé"""
    __tablename__ = 'ptc'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    duree_jours = db.Column(db.Integer, nullable=False)
    objet = db.Column(db.String(500), nullable=False)
    lieu_ptc = db.Column(db.String(200), nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='ptcs')

    def __repr__(self):
        return f'<PTC {self.matricule} - {self.date_debut} à {self.date_fin} ({self.duree_jours} jours)>'

class Desertion(db.Model):
    """Table métier : désertions du personnel"""
    __tablename__ = 'desertion'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_absence = db.Column(db.Date, nullable=False)
    date_retour = db.Column(db.Date, nullable=True)
    date_arret_solde = db.Column(db.Date, nullable=True)
    date_prise_solde = db.Column(db.Date, nullable=True)
    motif = db.Column(db.Text, nullable=True)
    circonstances = db.Column(db.Text, nullable=True)
    sanctions = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='desertions')

    def __repr__(self):
        return f'<Desertion {self.matricule} - {self.date_absence}>'

    @property
    def duree_jours(self):
        """Calcule la durée de désertion en jours"""
        if self.date_absence:
            end_date = self.date_retour if self.date_retour else date.today()
            return (end_date - self.date_absence).days + 1
        return None

    @property
    def en_cours(self):
        """Indique si la désertion est en cours"""
        return self.date_retour is None

class Detachement(db.Model):
    """Table métier : détachements du personnel"""
    __tablename__ = 'detachement'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=True)
    lieu_detachement = db.Column(db.String(200), nullable=False)
    adresse_detachement = db.Column(db.String(500), nullable=True)
    pays = db.Column(db.String(100), nullable=False, default='Maroc')
    organisme_accueil = db.Column(db.String(200), nullable=True)
    fonction_detachement = db.Column(db.String(200), nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='detachements')

    def __repr__(self):
        return f'<Detachement {self.matricule} - {self.lieu_detachement} ({self.date_debut})>'

    @property
    def duree_jours(self):
        """Calcule la durée de détachement en jours"""
        if self.date_debut:
            end_date = self.date_fin if self.date_fin else date.today()
            return (end_date - self.date_debut).days + 1
        return None

    @property
    def en_cours(self):
        """Indique si le détachement est en cours"""
        return self.date_fin is None

class Permission(db.Model):
    """Table métier : permissions du personnel"""
    __tablename__ = 'permission'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    type_absence_id = db.Column(db.Integer, db.ForeignKey('referentiel_type_absence.id_type'), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    duree_jours = db.Column(db.Integer, nullable=False)
    adresse_permission = db.Column(db.String(500), nullable=True)
    numero_serie = db.Column(db.String(50), nullable=True)
    motif = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='permissions')
    type_absence = db.relationship('ReferentielTypeAbsence', backref='permissions')

    def __repr__(self):
        return f'<Permission {self.matricule} - {self.type_absence.libelle if self.type_absence else ""} ({self.date_debut})>'

class MutationInterBie(db.Model):
    """Table métier : mutations inter-bataillons avec fonctions chronologiques"""
    __tablename__ = 'mutation_inter_bie'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    unite_origine_id = db.Column(db.Integer, db.ForeignKey('referentiel_unite.id_unite'), nullable=False)
    unite_destination_id = db.Column(db.Integer, db.ForeignKey('referentiel_unite.id_unite'), nullable=False)
    fonction_origine = db.Column(db.String(200), nullable=False)
    fonction_destination = db.Column(db.String(200), nullable=False)
    date_mutation = db.Column(db.Date, nullable=False)
    numero_decision = db.Column(db.String(50), nullable=True)
    motif = db.Column(db.Text, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='mutations_inter_bie')
    unite_origine = db.relationship('ReferentielUnite', foreign_keys=[unite_origine_id], backref='mutations_origine')
    unite_destination = db.relationship('ReferentielUnite', foreign_keys=[unite_destination_id], backref='mutations_destination')

    def __repr__(self):
        return f'<MutationInterBie {self.matricule} - {self.unite_origine.code if self.unite_origine else ""} → {self.unite_destination.code if self.unite_destination else ""} ({self.date_mutation})>'

class SejourOperationnel(db.Model):
    """Table métier : séjours opérationnels du personnel"""
    __tablename__ = 'sejour_operationnel'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    unite_operationnelle = db.Column(db.String(200), nullable=False)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=True)
    lieu_operation = db.Column(db.String(200), nullable=False)
    type_operation = db.Column(db.String(100), nullable=True)
    fonction_operationnelle = db.Column(db.String(200), nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='sejours_operationnels')

    def __repr__(self):
        return f'<SejourOperationnel {self.matricule} - {self.unite_operationnelle} ({self.date_debut})>'

    @property
    def duree_jours(self):
        """Calcule la durée du séjour opérationnel en jours"""
        if self.date_debut:
            end_date = self.date_fin if self.date_fin else date.today()
            return (end_date - self.date_debut).days + 1
        return None

    @property
    def en_cours(self):
        """Indique si le séjour opérationnel est en cours"""
        return self.date_fin is None

class Liberation(db.Model):
    """Table métier : libérations du personnel"""
    __tablename__ = 'liberation'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    date_liberation = db.Column(db.Date, nullable=False)
    motif_liberation = db.Column(db.String(200), nullable=False)
    numero_decision = db.Column(db.String(50), nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='liberations')

    def __repr__(self):
        return f'<Liberation {self.matricule} - {self.motif_liberation} ({self.date_liberation})>'

class Sanction(db.Model):
    """Table métier : sanctions disciplinaires du personnel"""
    __tablename__ = 'sanction'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    matricule = db.Column(db.String(20), db.ForeignKey('personnel.matricule'), nullable=False)
    type_sanction = db.Column(db.String(100), nullable=False)
    date_sanction = db.Column(db.Date, nullable=False)
    duree_jours = db.Column(db.Integer, nullable=True)
    motif = db.Column(db.Text, nullable=False)
    autorite_sanctionnante = db.Column(db.String(200), nullable=True)
    numero_decision = db.Column(db.String(50), nullable=True)
    date_execution = db.Column(db.Date, nullable=True)
    date_fin_execution = db.Column(db.Date, nullable=True)
    observations = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relations
    personnel = db.relationship('Personnel', backref='sanctions')

    def __repr__(self):
        return f'<Sanction {self.matricule} - {self.type_sanction} ({self.date_sanction})>'

    @property
    def en_cours_execution(self):
        """Indique si la sanction est en cours d'exécution"""
        if self.date_execution and not self.date_fin_execution:
            return date.today() >= self.date_execution
        return False
