"""
Script de mise à jour des référentiels Unités et Grades
Selon les spécifications exactes demandées
Division des Ressources Humaines - Inspection de l'Artillerie
"""

from app import app
from db import db
from gestion_vehicules.rh.models import ReferentielUnite, ReferentielGrade

def mettre_a_jour_unites():
    """Met à jour les unités selon les spécifications"""
    try:
        with app.app_context():
            print("🏢 Mise à jour des unités...")
            
            # Supprimer toutes les unités existantes
            ReferentielUnite.query.delete()
            
            # Nouvelles unités selon spécifications
            unites_data = [
                # GAR (1 à 26)
                ('1GAR', '1er Groupe d\'Artillerie', '1GAR'),
                ('2GAR', '2ème Groupe d\'Artillerie', '2GAR'),
                ('3GAR', '3ème Groupe d\'Artillerie', '3GAR'),
                ('4GAR', '4ème Groupe d\'Artillerie', '4GAR'),
                ('5GAR', '5ème Groupe d\'Artillerie', '5GAR'),
                ('6GAR', '6ème Groupe d\'Artillerie', '6GAR'),
                ('7GAR', '7ème Groupe d\'Artillerie', '7GAR'),
                ('8GAR', '8ème Groupe d\'Artillerie', '8GAR'),
                ('9GAR', '9ème Groupe d\'Artillerie', '9GAR'),
                ('10GAR', '10ème Groupe d\'Artillerie', '10GAR'),
                ('11GAR', '11ème Groupe d\'Artillerie', '11GAR'),
                ('12GAR', '12ème Groupe d\'Artillerie', '12GAR'),
                ('13GAR', '13ème Groupe d\'Artillerie', '13GAR'),
                ('14GAR', '14ème Groupe d\'Artillerie', '14GAR'),
                ('15GAR', '15ème Groupe d\'Artillerie', '15GAR'),
                ('16GAR', '16ème Groupe d\'Artillerie', '16GAR'),
                ('17GAR', '17ème Groupe d\'Artillerie', '17GAR'),
                ('18GAR', '18ème Groupe d\'Artillerie', '18GAR'),
                ('19GAR', '19ème Groupe d\'Artillerie', '19GAR'),
                ('20GAR', '20ème Groupe d\'Artillerie', '20GAR'),
                ('21GAR', '21ème Groupe d\'Artillerie', '21GAR'),
                ('22GAR', '22ème Groupe d\'Artillerie', '22GAR'),
                ('23GAR', '23ème Groupe d\'Artillerie', '23GAR'),
                ('24GAR', '24ème Groupe d\'Artillerie', '24GAR'),
                ('25GAR', '25ème Groupe d\'Artillerie', '25GAR'),
                ('26GAR', '26ème Groupe d\'Artillerie', '26GAR'),
                
                # Unités spécialisées
                ('INSPART', 'Inspection de l\'Artillerie', 'INSPART'),
                ('ERART', 'École Royale d\'Artillerie', 'ERART'),
                ('GSA', 'Groupement de Soutien de l\'Artillerie', 'GSA'),
                ('CFA', 'Centre de Formation de l\'Artillerie', 'CFA'),
                
                # Bureaux (1 à 5)
                ('1BUR', '1er Bureau', '1BUR'),
                ('2BUR', '2ème Bureau', '2BUR'),
                ('3BUR', '3ème Bureau', '3BUR'),
                ('4BUR', '4ème Bureau', '4BUR'),
                ('5BUR', '5ème Bureau', '5BUR'),
                
                # Bureaux spécialisés
                ('BREC', 'Bureau de Recrutement', 'BREC'),
                ('BCOUR', 'Bureau Courrier', 'BCOUR'),
                ('DPO', 'Direction du Personnel et de l\'Organisation', 'DPO'),
                ('PCA', 'Poste de Commandement Avancé', 'PCA'),
                
                # États-majors
                ('EMZS', 'État-Major Zone Sud', 'EMZS'),
                ('EMZE', 'État-Major Zone Est', 'EMZE'),
                
                # Services opérationnels
                ('SOPTAF', 'Service Opérationnel des Forces Terrestres', 'SOPTAF'),
                ('SOPSAG', 'Service Opérationnel de la Sécurité et de la Garde', 'SOPSAG'),
                ('SORIENT', 'Service Oriental', 'SORIENT'),
                
                # Autre
                ('AUTRE', 'Autre', 'AUTRE')
            ]
            
            # Création des unités
            for i, (code, libelle, code_court) in enumerate(unites_data, 1):
                unite = ReferentielUnite(
                    code=code,
                    libelle=libelle,
                    code_court=code_court,
                    description=f"Unité: {libelle}",
                    ordre=i
                )
                db.session.add(unite)
            
            db.session.commit()
            print(f"✅ {len(unites_data)} unités créées")
            
    except Exception as e:
        print(f"❌ Erreur mise à jour unités: {str(e)}")
        db.session.rollback()

def mettre_a_jour_grades():
    """Met à jour les grades selon les spécifications"""
    try:
        with app.app_context():
            print("🎖️ Mise à jour des grades...")
            
            # Supprimer tous les grades existants
            ReferentielGrade.query.delete()
            
            # Nouveaux grades selon spécifications (ordre hiérarchique)
            grades_data = [
                # Grades par ordre hiérarchique croissant
                ('SOL1', 'Soldat 1ère Classe', 'Soldat 1ère Classe', 1),
                ('SOL2', 'Soldat 2ème Classe', 'Soldat 2ème Classe', 2),
                ('BRG', 'Brigadier', 'Brigadier', 3),
                ('BRGC', 'Brigadier Chef', 'Brigadier Chef', 4),
                ('MDL', 'MDL', 'MDL', 5),
                ('MDLC', 'MDL Chef', 'MDL Chef', 6),
                ('ADJ', 'Adjudant', 'Adjudant', 7),
                ('ADJC', 'Adjudant Chef', 'Adjudant Chef', 8),
                ('SLT', 'Sous-Lieutenant', 'Sous-Lieutenant', 9),
                ('LTN', 'Lieutenant', 'Lieutenant', 10),
                ('CPT', 'Capitaine', 'Capitaine', 11),
                ('CDT', 'Commandant', 'Commandant', 12),
                ('LCL', 'Lieutenant-Colonel', 'Lieutenant-Colonel', 13),
                ('COL', 'Colonel', 'Colonel', 14)
            ]
            
            # Création des grades
            for code_grade, libelle, libelle_court, niveau in grades_data:
                grade = ReferentielGrade(
                    code_grade=code_grade,
                    libelle=libelle,
                    libelle_court=libelle_court,
                    niveau=niveau,
                    ordre=niveau,
                    description=f"Grade: {libelle}"
                )
                db.session.add(grade)
            
            db.session.commit()
            print(f"✅ {len(grades_data)} grades créés")
            
    except Exception as e:
        print(f"❌ Erreur mise à jour grades: {str(e)}")
        db.session.rollback()

def mettre_a_jour_personnel_existant():
    """Met à jour le personnel existant avec les nouveaux grades/unités"""
    try:
        with app.app_context():
            print("👥 Mise à jour du personnel existant...")
            
            from gestion_vehicules.rh.models import Personnel
            import random
            
            # Récupération des nouveaux référentiels
            grades = ReferentielGrade.query.all()
            unites = ReferentielUnite.query.all()
            
            if not grades or not unites:
                print("⚠️ Pas de grades ou unités disponibles")
                return
            
            # Mise à jour du personnel
            personnel = Personnel.query.all()
            for militaire in personnel:
                # Attribution aléatoire d'un nouveau grade et unité
                militaire.grade_actuel_id = random.choice(grades).id_grade
                militaire.unite_id = random.choice(unites).id_unite
            
            db.session.commit()
            print(f"✅ {len(personnel)} militaires mis à jour")
            
    except Exception as e:
        print(f"❌ Erreur mise à jour personnel: {str(e)}")
        db.session.rollback()

def afficher_statistiques():
    """Affiche les statistiques après mise à jour"""
    try:
        with app.app_context():
            print("\n📊 STATISTIQUES APRÈS MISE À JOUR:")
            print("=" * 50)
            
            # Unités
            unites = ReferentielUnite.query.order_by(ReferentielUnite.ordre).all()
            print(f"🏢 UNITÉS ({len(unites)}):")
            for unite in unites[:10]:  # Afficher les 10 premières
                print(f"   • {unite.code}: {unite.libelle}")
            if len(unites) > 10:
                print(f"   ... et {len(unites) - 10} autres")
            
            # Grades
            grades = ReferentielGrade.query.order_by(ReferentielGrade.niveau).all()
            print(f"\n🎖️ GRADES ({len(grades)}):")
            for grade in grades:
                print(f"   • {grade.code_grade}: {grade.libelle}")
            
            # Personnel
            from gestion_vehicules.rh.models import Personnel
            total_personnel = Personnel.query.count()
            print(f"\n👥 PERSONNEL: {total_personnel} militaires")
            
    except Exception as e:
        print(f"❌ Erreur affichage statistiques: {str(e)}")

def main():
    """Fonction principale"""
    print("🔧 MISE À JOUR DES RÉFÉRENTIELS RH")
    print("=" * 60)
    print("📋 Mise à jour selon spécifications:")
    print("   • Unités: 1GAR-26GAR, INSPART, ERART, etc.")
    print("   • Grades: Soldat 1ère classe → Colonel")
    print("=" * 60)
    
    # Confirmation
    reponse = input("\n❓ Continuer la mise à jour? (oui/non): ").lower().strip()
    if reponse not in ['oui', 'o', 'yes', 'y']:
        print("❌ Mise à jour annulée")
        return
    
    print("\n🚀 Début de la mise à jour...")
    
    # Mise à jour
    mettre_a_jour_unites()
    mettre_a_jour_grades()
    mettre_a_jour_personnel_existant()
    
    # Statistiques
    afficher_statistiques()
    
    print("\n🎉 MISE À JOUR TERMINÉE!")
    print("🔗 Testez l'application:")
    print("   • Recherche: http://localhost:3000/rh/recherche")
    print("   • Nouveau militaire: http://localhost:3000/rh/nouveau_militaire")

if __name__ == "__main__":
    main()
