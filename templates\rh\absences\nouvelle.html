{% extends "rh/base_rh.html" %}

{% block title %}Nouvelle Absence - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-calendar-plus"></i>
                                Nouvelle Demande d'Absence
                            </h2>
                            <small class="text-muted">Création d'une nouvelle demande d'absence ou permission</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.liste_absences') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à la Liste
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Création -->
    <form method="POST" class="needs-validation" novalidate>
        <div class="row">
            <!-- Section 1: Informations de Base -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations de Base
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Militaire Concerné *</label>
                            <select name="militaire_id" class="form-control form-control-military" required>
                                <option value="">Sélectionner un militaire...</option>
                                {% for militaire in personnel %}
                                <option value="{{ militaire.id }}">
                                    {{ militaire.grade_actuel or '' }} {{ militaire.nom }} {{ militaire.prenom }} - {{ militaire.unite_affectation or 'N/A' }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">Veuillez sélectionner un militaire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Type d'Absence *</label>
                            <select name="type_absence" class="form-control form-control-military" required>
                                <option value="">Sélectionner un type...</option>
                                {% for type_abs in types_absences %}
                                <option value="{{ type_abs }}">{{ type_abs }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">Veuillez sélectionner un type d'absence</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date de Début *</label>
                                <input type="date" name="date_debut" class="form-control form-control-military" required>
                                <div class="invalid-feedback">La date de début est obligatoire</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date de Fin *</label>
                                <input type="date" name="date_fin" class="form-control form-control-military" required>
                                <div class="invalid-feedback">La date de fin est obligatoire</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Durée Calculée</label>
                            <div class="input-group">
                                <input type="text" id="duree_calculee" class="form-control form-control-military" readonly>
                                <span class="input-group-text" style="background: var(--card-bg); border-color: var(--border-color); color: var(--text-light);">jour(s)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 2: Détails et Justification -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt"></i>
                            Détails et Justification
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Motif de l'Absence *</label>
                            <textarea name="motif" class="form-control form-control-military" rows="4" required 
                                      placeholder="Décrivez le motif de l'absence..."></textarea>
                            <div class="invalid-feedback">Le motif est obligatoire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Justificatif</label>
                            <input type="text" name="justificatif" class="form-control form-control-military" 
                                   placeholder="Référence du document justificatif (optionnel)">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Observations</label>
                            <textarea name="observations" class="form-control form-control-military" rows="3" 
                                      placeholder="Observations complémentaires (optionnel)"></textarea>
                        </div>
                        
                        <div class="alert alert-military">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information :</strong> Cette demande sera soumise pour approbation hiérarchique.
                            Assurez-vous que toutes les informations sont correctes.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section 3: Validation -->
        <div class="row">
            <div class="col-12">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-check"></i>
                            Validation et Soumission
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="alert alert-warning-military">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Attention :</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>Vérifiez les dates sélectionnées</li>
                                        <li>Assurez-vous que le motif est clair</li>
                                        <li>La demande sera en attente d'approbation</li>
                                        <li>Vous recevrez une notification de la décision</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-success-military btn-lg">
                                        <i class="fas fa-paper-plane"></i> Soumettre la Demande
                                    </button>
                                    <a href="{{ url_for('rh.liste_absences') }}" class="btn btn-secondary btn-lg">
                                        <i class="fas fa-times"></i> Annuler
                                    </a>
                                </div>
                                
                                <hr class="my-3">
                                
                                <div class="text-center">
                                    <h6 class="text-warning mb-2">Actions Rapides</h6>
                                    <div class="d-grid gap-1">
                                        <a href="{{ url_for('rh.dashboard') }}" class="btn btn-military btn-sm">
                                            <i class="fas fa-tachometer-alt"></i> Tableau de Bord
                                        </a>
                                        <a href="{{ url_for('rh.liste_absences') }}" class="btn btn-info-military btn-sm">
                                            <i class="fas fa-calendar-times"></i> Liste Absences
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control-military:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 213, 79, 0.25);
}

.invalid-feedback {
    color: var(--danger-color);
    font-weight: 600;
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-control:valid {
    border-color: var(--success-color);
}

.input-group-text {
    background: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-light);
}

.alert-warning-military {
    border-left: 4px solid var(--warning-color);
    background: rgba(255, 152, 0, 0.1);
    border-radius: 8px;
}

.alert-warning-military ul {
    padding-left: 1.2rem;
}

.card-military {
    transition: all 0.3s ease;
}

.card-military:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 213, 79, 0.15);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Validation en temps réel
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
    
})();

// Calcul automatique de la durée
function calculerDuree() {
    const dateDebut = document.querySelector('input[name="date_debut"]').value;
    const dateFin = document.querySelector('input[name="date_fin"]').value;
    const dureeField = document.getElementById('duree_calculee');
    
    if (dateDebut && dateFin) {
        const debut = new Date(dateDebut);
        const fin = new Date(dateFin);
        
        if (fin >= debut) {
            const diffTime = Math.abs(fin - debut);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            dureeField.value = diffDays;
            dureeField.style.color = 'var(--success-color)';
        } else {
            dureeField.value = 'Dates invalides';
            dureeField.style.color = 'var(--danger-color)';
        }
    } else {
        dureeField.value = '';
    }
}

// Événements pour le calcul de durée
document.querySelector('input[name="date_debut"]').addEventListener('change', calculerDuree);
document.querySelector('input[name="date_fin"]').addEventListener('change', calculerDuree);

// Validation des dates
document.querySelector('input[name="date_debut"]').addEventListener('change', function() {
    const dateFinInput = document.querySelector('input[name="date_fin"]');
    dateFinInput.min = this.value;
    
    if (dateFinInput.value && dateFinInput.value < this.value) {
        dateFinInput.value = this.value;
    }
    calculerDuree();
});

// Suggestions de motifs selon le type d'absence
document.querySelector('select[name="type_absence"]').addEventListener('change', function() {
    const motifTextarea = document.querySelector('textarea[name="motif"]');
    const suggestions = {
        'Permission': 'Demande de permission pour raisons personnelles.',
        'Maladie': 'Absence pour raisons médicales. Certificat médical joint.',
        'Maternité': 'Congé de maternité selon la réglementation en vigueur.',
        'Formation': 'Participation à une formation professionnelle.',
        'Mission': 'Mission officielle selon ordre de mission.',
        'Congé Exceptionnel': 'Congé exceptionnel pour circonstances particulières.'
    };
    
    if (suggestions[this.value] && !motifTextarea.value) {
        motifTextarea.value = suggestions[this.value];
    }
});

// Animation des cartes
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card-military');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    if (this.checkValidity()) {
        const confirmation = confirm('Êtes-vous sûr de vouloir soumettre cette demande d\'absence ?');
        if (!confirmation) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
