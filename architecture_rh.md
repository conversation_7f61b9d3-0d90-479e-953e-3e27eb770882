# Architecture de la base de données RH militaire

## 1. Tables de référence

### 1.1 referentiel_genre
- **id_genre** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(20) NOT NULL — ‘<PERSON><PERSON><PERSON><PERSON>’, ‘Féminin’

### 1.2 referentiel_categorie
- **id_categorie** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(50) NOT NULL — ‘Officier’, ‘Officier du rang’, ‘Militaire du rang’

### 1.3 referentiel_groupe_sanguin
- **id_groupe** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(3) NOT NULL — ‘A+’, ‘A−’, ‘B+’, ‘B−’, ‘AB+’, ‘AB−’, ‘O+’, ‘O−’

### 1.4 referentiel_arme
- **id_arme** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(50) NOT NULL — ‘Artillerie’, ‘Blindé’, ‘Infanterie’, ‘Transmission’, ‘Intendance’, ‘Cavalerie’, ‘Sant<PERSON>’, ‘<PERSON><PERSON><PERSON>’, ‘G<PERSON>ie’

### 1.5 referentiel_specialite
- **id_specialite** INT AUTO_INCREMENT PRIMARY KEY  
- **id_arme** INT NOT NULL REFERENCES referentiel_arme(id_arme)  
- **libelle** VARCHAR(50) NOT NULL — ‘sol-sol’, ‘sol-air’

### 1.6 referentiel_unite
- **id_unite** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(100) NOT NULL — Valeurs : ‘1°GAR’,‘2°GAR’,‘3°GAR’ …, ‘26°GAR’, ‘Inspection de l’Artillerie’, ‘ERART’, ‘GSA’, ‘CFA’, ‘1°Bureau’, …, ‘5°Bureau’, ‘Bureau de recrutement’, ‘Bureau courrier’, ‘DPO’, ‘PCA’, ‘Etat major zone sud’, ‘Etat major zone EST’, ‘SOPTAF’, ‘SOPSAG’, ‘S.ORIENTAL’, ‘Autre’

### 1.7 referentiel_grade
- **id_grade** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(50) NOT NULL — Valeurs : ‘soldat 1°classe’, ‘soldat 2° classe’, ‘brigadier’, ‘brigadier chef’, ‘MDL’, ‘MDL Chef’, ‘adjudant’, ‘adjudant chef’, ‘sous-lieutenant’, ‘lieutenant’, ‘capitaine’, ‘commandant’, ‘lt-colonel’, ‘colonel’

### 1.8 referentiel_situation_familiale
- **id_sitfam** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(20) NOT NULL — ‘Célibataire’, ‘Marié(e)’, ‘Divorcé(e)’, ‘Veuf/Veuve’

### 1.9 referentiel_degre_parente
- **id_degre** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(20) NOT NULL — ‘Père’, ‘Mère’, ‘Fils’, ‘Fille’, ‘Frère’, ‘Sœur’, ‘Grand-père’, ‘Grand-mère’

### 1.10 referentiel_langue
- **id_langue** INT AUTO_INCREMENT PRIMARY KEY  
- **libelle** VARCHAR(20) NOT NULL — ‘Français’, ‘Anglais’, ‘Espagnol’, ‘Allemand’, ‘Italien’, ‘Portugais’, ‘Chinois’, ‘Japonais’, ‘Russe’

## 2. Tables de données

### 2.1 personnel
- **matricule** VARCHAR(20) PRIMARY KEY  
- **nom** VARCHAR(100) NOT NULL  
- **prenom** VARCHAR(100) NOT NULL  
- **nom_arabe** VARCHAR(100) NOT NULL  
- **prenom_arabe** VARCHAR(100) NOT NULL  
- **date_naissance** DATE NOT NULL  
- **lieu_naissance** VARCHAR(100) NOT NULL  
- **sexe_id** INT NOT NULL REFERENCES referentiel_genre(id_genre)  
- **categorie_id** INT NOT NULL REFERENCES referentiel_categorie(id_categorie)  
- **groupe_sanguin_id** INT NOT NULL REFERENCES referentiel_groupe_sanguin(id_groupe)  
- **numero_cin** VARCHAR(20) NOT NULL  
- **date_delivrance_cin** DATE NOT NULL  
- **date_expiration_cin** DATE NOT NULL  
- **gsm** VARCHAR(20) NOT NULL  
- **telephone_domicile** VARCHAR(20) NULL  
- **taille** DECIMAL(5,2) NOT NULL  
- **lieu_residence** VARCHAR(150) NOT NULL  
- **arme_id** INT NOT NULL REFERENCES referentiel_arme(id_arme)  
- **specialite_id** INT NULL REFERENCES referentiel_specialite(id_specialite)  
- **unite_id** INT NOT NULL REFERENCES referentiel_unite(id_unite)  
- **grade_actuel_id** INT NOT NULL REFERENCES referentiel_grade(id_grade)  
- **fonction** VARCHAR(100) NOT NULL  
- **date_prise_fonction** DATE NOT NULL  
- **ccp** VARCHAR(50) NOT NULL  
- **compte_bancaire** VARCHAR(50) NULL  
- **numero_somme** VARCHAR(50) NOT NULL  
- **date_engagement** DATE NOT NULL  
- **nom_pere** VARCHAR(100) NOT NULL  
- **prenom_pere** VARCHAR(100) NOT NULL  
- **nom_mere** VARCHAR(100) NOT NULL  
- **prenom_mere** VARCHAR(100) NOT NULL  
- **adresse_parents** VARCHAR(200) NOT NULL  
- **situation_fam_id** INT NOT NULL REFERENCES referentiel_situation_familiale(id_sitfam)  
- **nombre_enfants** INT NULL  
- **numero_passport** VARCHAR(50) NULL  
- **date_delivrance_passport** DATE NULL  
- **date_expiration_passport** DATE NULL  
- **gsm_urgence** VARCHAR(20) NOT NULL  
- **degre_parente_id** INT NOT NULL REFERENCES referentiel_degre_parente(id_degre)

### 2.2 personnel_langue
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **langue_id** INT NOT NULL REFERENCES referentiel_langue(id_langue)  
- PRIMARY KEY (matricule, langue_id)

### 2.3 conjoint(e)
- **id_conjoint(e)** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL UNIQUE REFERENCES personnel(matricule)  
- **nom** VARCHAR(100) NOT NULL  
- **prenom** VARCHAR(100) NOT NULL  
- **nom_arabe** VARCHAR(100) NOT NULL  
- **prenom_arabe** VARCHAR(100) NOT NULL  
- **date_naissance** DATE NOT NULL  
- **lieu_naissance** VARCHAR(100) NOT NULL  
- **lieu_naissance_arabe** VARCHAR(100) NOT NULL  
- **adresse** VARCHAR(200) NOT NULL  
- **adresse_arabe** VARCHAR(200) NOT NULL  
- **date_mariage** DATE NOT NULL  
- **lieu_mariage** VARCHAR(100) NOT NULL  
- **profession** VARCHAR(100) NOT NULL  
- **profession_arabe** VARCHAR(100) NOT NULL  
- **numero_cin** VARCHAR(20) NOT NULL  
- **gsm** VARCHAR(20) NOT NULL  
- **nom_pere** VARCHAR(100) NOT NULL  
- **prenom_pere** VARCHAR(100) NOT NULL  
- **nom_arabe_pere** VARCHAR(100) NOT NULL  
- **prenom_arabe_pere** VARCHAR(100) NOT NULL  
- **nom_mere** VARCHAR(100) NOT NULL  
- **prenom_mere** VARCHAR(100) NOT NULL  
- **nom_arabe_mere** VARCHAR(100) NOT NULL  
- **prenom_arabe_mere** VARCHAR(100) NOT NULL  
- **profession_pere** VARCHAR(100) NOT NULL  
- **profession_mere** VARCHAR(100) NOT NULL

### 2.4 enfant
- **id_enfant** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **nom** VARCHAR(100) NOT NULL  
- **prenom** VARCHAR(100) NOT NULL  
- **sexe_id** INT NOT NULL REFERENCES referentiel_genre(id_genre)  
- **date_naissance** DATE NOT NULL  
- **lieu_naissance** VARCHAR(100) NOT NULL  
- **date_deces** DATE NULL

### 2.5 situation_medicale
- **id_sitmed** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL UNIQUE REFERENCES personnel(matricule)  
- **maladies** TEXT NOT NULL  
- **date_hospitalisation** DATE NOT NULL  
- **lieu_hospitalisation** VARCHAR(100) NOT NULL  
- **aptitude** ENUM('apte','inapte') NOT NULL  
- **observations** TEXT NULL

### 2.6 vaccination
- **id_vaccination** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **date_vaccination** DATE NOT NULL  
- **objet** VARCHAR(100) NOT NULL  
- **observation** TEXT NULL

### 2.7 ptc
- **id_ptc** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **date_ptc** DATE NOT NULL  
- **duree** INT NOT NULL — en jours  
- **date_debut** DATE NOT NULL  
- **date_fin** DATE NOT NULL  
- **objet** VARCHAR(100) NOT NULL  
- **observations** TEXT NULL

### 2.8 permission
- **id_permission** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **date_debut** DATE NOT NULL  
- **date_fin** DATE NOT NULL  
- **adresse** VARCHAR(200) NOT NULL  
- **numero_serie** VARCHAR(50) NOT NULL

### 2.9 desertion
- **id_desertion** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **date_absence** DATE NOT NULL  
- **date_desertion** DATE NOT NULL  
- **date_retour** DATE NOT NULL  
- **date_arret_solde** DATE NULL  
- **date_prise_solde** DATE NULL

### 2.10 detachement
- **id_detachement** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **date_debut** DATE NOT NULL  
- **adresse_detachement** VARCHAR(200) NOT NULL  
- **pays** VARCHAR(100) NOT NULL  
- **date_fin** DATE NOT NULL

### 2.11 mutation_fonction
- **id_mutation** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **service_id** INT NOT NULL REFERENCES referentiel_arme(id_arme)  
- **fonction** VARCHAR(100) NOT NULL  
- **date_debut** DATE NOT NULL  
- **date_fin** DATE NULL

### 2.12 sejour_ops
- **id_sejour_ops** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **unite_id** INT NOT NULL REFERENCES referentiel_unite(id_unite)  
- **date_debut** DATE NOT NULL  
- **date_fin** DATE NOT NULL

### 2.13 liberation
- **id_liberation** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **motif** VARCHAR(200) NOT NULL  
- **date_liberation** DATE NOT NULL  
- **observation** TEXT NULL

### 2.14 avancement
- **id_avancement** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **grade_precedent_id** INT NOT NULL REFERENCES referentiel_grade(id_grade)  
- **grade_suivant_id** INT NOT NULL REFERENCES referentiel_grade(id_grade)  
- **date_avancement** DATE NOT NULL  
- **conditions** TEXT NULL

### 2.15 sanction
- **id_sanction** INT AUTO_INCREMENT PRIMARY KEY  
- **matricule** VARCHAR(20) NOT NULL REFERENCES personnel(matricule)  
- **date_sanction** DATE NOT NULL  
- **type_sanction** ENUM('sanction','punition') NOT NULL  
- **duree** INT NULL — en jours  
- **motif** VARCHAR(200) NOT NULL  
- **observation** TEXT NULL

## 3. Diagramme Mermaid des relations

```mermaid
erDiagram
    referentiel_genre {
        INT id_genre PK
        VARCHAR libelle "Masculin","Féminin"
    }
    referentiel_categorie {
        INT id_categorie PK
        VARCHAR libelle "Officier","Officier du rang","Militaire du rang"
    }
    referentiel_groupe_sanguin {
        INT id_groupe PK
        VARCHAR libelle "A+","A−","B+","B−","AB+","AB−","O+","O−"
    }
    referentiel_service {
        INT id_service PK
        VARCHAR libelle "Artillerie","Blindé","Infanterie","Transmission","Intendance","Cavalerie","Santé","Matériel","Génie"
    }
    referentiel_specialite {
        INT id_specialite PK
        INT service_id FK
        VARCHAR libelle "sol-sol","sol-air"
    }
    referentiel_unite {
        INT id_unite PK
        VARCHAR libelle "1°GAR","2°GAR","3°GAR","4°GAR","5°GAR","6°GAR","7°GAR","8°GAR","9°GAR","10°GAR","11°GAR","12°GAR","13°GAR","14°GAR","15°GAR","16°GAR","17°GAR","18°GAR","19°GAR","20°GAR","21°GAR","22°GAR","23°GAR","24°GAR","25°GAR","26°GAR","Inspection de l'Artillerie","ERART","GSA","CFA","1°Bureau","2°Bureau","3°Bureau","4°Bureau","5°Bureau","Bureau de recrutement","bureau courrier","DPO","PCA","Etat major zone sud","Etat major zone EST","SOPTAF","SOPSAG","S.ORIENTAL","Autre"
    }
    referentiel_grade {
        INT id_grade PK
        VARCHAR libelle "soldat 1°classe","soldat 2° classe","brigadier","brigadier chef","MDL","MDL Chef","adjudant","adjudant chef","sous-lieutenant","lieutenant","capitaine","commandant","lt-colonel","colonel"
    }
    referentiel_etat_matrimonial {
        INT id_etat PK
        VARCHAR libelle "Célibataire","Marié(e)","Divorcé(e)","Veuf / Veuve"
    }
    referentiel_langue {
        INT id_langue PK
        VARCHAR libelle
    }
    referentiel_degre_parente {
        INT id_degre PK
        VARCHAR libelle
    }

    personnel {
        VARCHAR matricule PK
        /* autres champs inchangés */
    }

    referentiel_genre ||--o{ personnel : genre
    referentiel_categorie ||--o{ personnel : categorie
    referentiel_groupe_sanguin ||--o{ personnel : groupe_sanguin
    referentiel_service ||--o{ personnel : service
    referentiel_specialite ||--o{ personnel : specialite
    referentiel_unite ||--o{ personnel : unite
    referentiel_grade ||--o{ personnel : grade_actuel
    referentiel_etat_matrimonial ||--o{ personnel : etat_matrimonial
```
