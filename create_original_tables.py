#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Création des tables originales du checkpoint 70
Pour restaurer le fonctionnement exact de l'application
"""

import mysql.connector
from mysql.connector import Error

def create_original_tables():
    """Crée les tables originales du checkpoint 70"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("🔧 Création des tables originales du checkpoint 70...")
        
        # Désactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0;")
        
        # Tables originales avec les noms exacts du checkpoint 70
        tables_sql = [
            # Table situation_medicale_original
            """
            CREATE TABLE IF NOT EXISTS situation_medicale_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                aptitude ENUM('Apte', 'Inapte') NOT NULL DEFAULT 'Apte',
                date_derniere_visite DATE NULL,
                observations_generales TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table hospitalisation_original
            """
            CREATE TABLE IF NOT EXISTS hospitalisation_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                date_entree DATE NOT NULL,
                date_sortie DATE NULL,
                etablissement VARCHAR(200) NOT NULL,
                motif VARCHAR(500) NOT NULL,
                diagnostic TEXT NULL,
                traitement TEXT NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table vaccination_original
            """
            CREATE TABLE IF NOT EXISTS vaccination_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                nom_vaccin VARCHAR(100) NOT NULL,
                date_vaccination DATE NOT NULL,
                numero_lot VARCHAR(50) NULL,
                lieu_vaccination VARCHAR(200) NULL,
                medecin_vaccinateur VARCHAR(100) NULL,
                rappel_prevu DATE NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table ptc_original
            """
            CREATE TABLE IF NOT EXISTS ptc_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                duree_jours INT NOT NULL,
                objet VARCHAR(500) NOT NULL,
                lieu_ptc VARCHAR(200) NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table conjoint_original
            """
            CREATE TABLE IF NOT EXISTS conjoint_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                nom VARCHAR(80) NOT NULL,
                prenom VARCHAR(80) NOT NULL,
                nom_ar VARCHAR(80) NOT NULL,
                prenom_ar VARCHAR(80) NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                cin_numero VARCHAR(20) NOT NULL,
                cin_date_delivrance DATE NOT NULL,
                cin_date_expiration DATE NOT NULL,
                nom_pere VARCHAR(80) NOT NULL,
                prenom_pere VARCHAR(80) NOT NULL,
                nom_mere VARCHAR(80) NOT NULL,
                prenom_mere VARCHAR(80) NOT NULL,
                profession VARCHAR(100) NULL,
                lieu_travail VARCHAR(200) NULL,
                gsm VARCHAR(20) NULL,
                date_mariage DATE NOT NULL,
                lieu_mariage VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table enfant_original
            """
            CREATE TABLE IF NOT EXISTS enfant_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                nom VARCHAR(80) NOT NULL,
                prenom VARCHAR(80) NOT NULL,
                nom_ar VARCHAR(80) NOT NULL,
                prenom_ar VARCHAR(80) NOT NULL,
                genre_id INT NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                date_deces DATE NULL,
                lieu_deces VARCHAR(100) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                FOREIGN KEY (genre_id) REFERENCES referentiel_genre(id_genre)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table permission_original
            """
            CREATE TABLE IF NOT EXISTS permission_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                type_absence_id INT NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                duree_jours INT NOT NULL,
                adresse_permission VARCHAR(500) NULL,
                numero_serie VARCHAR(50) NULL,
                motif TEXT NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                FOREIGN KEY (type_absence_id) REFERENCES referentiel_type_absence(id_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table desertion_original
            """
            CREATE TABLE IF NOT EXISTS desertion_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                date_absence DATE NOT NULL,
                date_retour DATE NULL,
                date_arret_solde DATE NULL,
                date_prise_solde DATE NULL,
                motif TEXT NULL,
                circonstances TEXT NULL,
                sanctions TEXT NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table detachement_original
            """
            CREATE TABLE IF NOT EXISTS detachement_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                lieu_detachement VARCHAR(200) NOT NULL,
                adresse_detachement VARCHAR(500) NULL,
                pays VARCHAR(100) NOT NULL DEFAULT 'Maroc',
                organisme_accueil VARCHAR(200) NULL,
                fonction_detachement VARCHAR(200) NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table mutation_inter_bie_original
            """
            CREATE TABLE IF NOT EXISTS mutation_inter_bie_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                unite_origine_id INT NOT NULL,
                unite_destination_id INT NOT NULL,
                fonction_origine VARCHAR(200) NOT NULL,
                fonction_destination VARCHAR(200) NOT NULL,
                date_mutation DATE NOT NULL,
                numero_decision VARCHAR(50) NULL,
                motif TEXT NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                FOREIGN KEY (unite_origine_id) REFERENCES referentiel_unite(id_unite),
                FOREIGN KEY (unite_destination_id) REFERENCES referentiel_unite(id_unite)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table sejour_operationnel_original
            """
            CREATE TABLE IF NOT EXISTS sejour_operationnel_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                unite_operationnelle VARCHAR(200) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                lieu_operation VARCHAR(200) NOT NULL,
                type_operation VARCHAR(100) NULL,
                fonction_operationnelle VARCHAR(200) NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table liberation_original
            """
            CREATE TABLE IF NOT EXISTS liberation_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                date_liberation DATE NOT NULL,
                motif_liberation VARCHAR(200) NOT NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table sanction_original
            """
            CREATE TABLE IF NOT EXISTS sanction_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                type_sanction VARCHAR(100) NOT NULL,
                date_sanction DATE NOT NULL,
                duree_jours INT NULL,
                motif TEXT NOT NULL,
                autorite_sanctionnante VARCHAR(200) NULL,
                numero_decision VARCHAR(50) NULL,
                date_execution DATE NULL,
                date_fin_execution DATE NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table historique_grade_original
            """
            CREATE TABLE IF NOT EXISTS historique_grade_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                grade_id INT NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                FOREIGN KEY (grade_id) REFERENCES referentiel_grade(id_grade)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """,
            
            # Table personnel_langue_original
            """
            CREATE TABLE IF NOT EXISTS personnel_langue_original (
                id INT PRIMARY KEY AUTO_INCREMENT,
                matricule VARCHAR(20) NOT NULL,
                langue_id INT NOT NULL,
                niveau ENUM('Débutant', 'Intermédiaire', 'Avancé', 'Courant') NOT NULL,
                date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule) ON DELETE CASCADE,
                FOREIGN KEY (langue_id) REFERENCES referentiel_langue(id_langue)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """
        ]
        
        # Créer chaque table
        for i, table_sql in enumerate(tables_sql, 1):
            try:
                cursor.execute(table_sql)
                table_name = table_sql.split('CREATE TABLE IF NOT EXISTS ')[1].split(' (')[0].strip()
                print(f"✓ Table {i}/15 créée : {table_name}")
            except Error as e:
                table_name = table_sql.split('CREATE TABLE IF NOT EXISTS ')[1].split(' (')[0].strip()
                print(f"✗ Erreur table {table_name}: {e}")
        
        # Réactiver les contraintes
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1;")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n✅ Tables originales du checkpoint 70 créées avec succès !")
        return True
        
    except Error as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    print("🎯 CRÉATION DES TABLES ORIGINALES DU CHECKPOINT 70")
    print("=" * 60)
    
    success = create_original_tables()
    
    if success:
        print("\n🎉 Tables originales créées avec succès !")
        print("L'application devrait maintenant fonctionner comme au checkpoint 70.")
    else:
        print("\n❌ Échec de la création des tables")
