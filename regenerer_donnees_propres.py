#!/usr/bin/env python3
"""
Script pour supprimer toutes les données actuelles et générer de nouvelles données de test propres
avec des IDs cohérents et personnalisés
"""

import mysql.connector
from mysql.connector import Error
import requests
import json
from datetime import datetime, date, timedelta

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion_vehicules'
}

BASE_URL = "http://127.0.0.1:3000"

def supprimer_toutes_donnees():
    """Supprimer toutes les données de courrier existantes"""
    print("🗑️ Suppression de toutes les données existantes...")
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Supprimer dans l'ordre pour respecter les contraintes de clés étrangères
        cursor.execute("DELETE FROM courrier_division_info")
        cursor.execute("DELETE FROM courrier_division_action")
        cursor.execute("DELETE FROM courrier_arrive")
        cursor.execute("DELETE FROM courrier_envoye")
        cursor.execute("DELETE FROM courrier")
        
        # Réinitialiser les auto-increment
        cursor.execute("ALTER TABLE courrier_arrive AUTO_INCREMENT = 1")
        cursor.execute("ALTER TABLE courrier_envoye AUTO_INCREMENT = 1")
        cursor.execute("ALTER TABLE courrier AUTO_INCREMENT = 1")
        cursor.execute("ALTER TABLE courrier_division_action AUTO_INCREMENT = 1")
        cursor.execute("ALTER TABLE courrier_division_info AUTO_INCREMENT = 1")
        
        connection.commit()
        print("✅ Toutes les données supprimées")
        
    except Error as e:
        print(f"❌ Erreur lors de la suppression: {e}")
        if connection:
            connection.rollback()
    
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def generer_courriers_arrives():
    """Générer des courriers arrivés avec des IDs propres"""
    print("\n📥 Génération des courriers arrivés...")
    
    # Date de base pour les courriers
    date_base = date(2024, 1, 15)
    
    courriers_arrives = [
        {
            "id": "1001",
            "urgence": "urgent",
            "nature": "message",
            "date_arrivee": (date_base + timedelta(days=0)).strftime('%Y-%m-%d'),
            "date_signature": (date_base - timedelta(days=1)).strftime('%Y-%m-%d'),
            "numero_ecrit": "MSG-2024-001",
            "expediteur": "1° Bureau",
            "objet": "Demande d'application de gestion de courrier",
            "classification": "public",
            "annotation": "Traitement prioritaire demandé",
            "divisions_action": ["technique", "informatique"],
            "divisions_info": ["instruction"]
        },
        {
            "id": "1002",
            "urgence": "routine",
            "nature": "nds",
            "date_arrivee": (date_base + timedelta(days=1)).strftime('%Y-%m-%d'),
            "date_signature": date_base.strftime('%Y-%m-%d'),
            "numero_ecrit": "NDS-2024-002",
            "expediteur": "2° Bureau",
            "objet": "Test de déploiement du nouveau système",
            "classification": "restreint",
            "annotation": "Phase de test en cours",
            "divisions_action": ["informatique"],
            "divisions_info": ["technique", "planification"]
        },
        {
            "id": "1003",
            "urgence": "urgent",
            "nature": "decision",
            "date_arrivee": (date_base + timedelta(days=2)).strftime('%Y-%m-%d'),
            "date_signature": (date_base + timedelta(days=1)).strftime('%Y-%m-%d'),
            "numero_ecrit": "DEC-2024-003",
            "expediteur": "3° Bureau",
            "objet": "Projet de modernisation des systèmes informatiques",
            "classification": "confidentiel",
            "annotation": "Validation du commandement requise",
            "divisions_action": ["planification", "technique"],
            "divisions_info": ["instruction", "mcpo"]
        },
        {
            "id": "1004",
            "urgence": "extreme_urgent",
            "nature": "note_royale",
            "date_arrivee": (date_base + timedelta(days=3)).strftime('%Y-%m-%d'),
            "date_signature": (date_base + timedelta(days=2)).strftime('%Y-%m-%d'),
            "numero_ecrit": "NR-2024-004",
            "expediteur": "4° Bureau",
            "objet": "Directive sur la sécurité des systèmes d'information",
            "classification": "secret",
            "annotation": "Application immédiate",
            "divisions_action": ["informatique", "technique"],
            "divisions_info": ["instruction", "planification", "asa"]
        },
        {
            "id": "1005",
            "urgence": "routine",
            "nature": "message",
            "date_arrivee": (date_base + timedelta(days=4)).strftime('%Y-%m-%d'),
            "date_signature": (date_base + timedelta(days=3)).strftime('%Y-%m-%d'),
            "numero_ecrit": "MSG-2024-005",
            "expediteur": "5° Bureau",
            "objet": "Demande de formation du personnel technique",
            "classification": "public",
            "annotation": "Formation prévue pour février",
            "divisions_action": ["instruction"],
            "divisions_info": ["rh", "technique"]
        }
    ]
    
    for courrier in courriers_arrives:
        try:
            response = requests.post(
                f"{BASE_URL}/api/courriers/arrives",
                headers={"Content-Type": "application/json"},
                data=json.dumps(courrier)
            )
            
            if response.status_code == 201:
                print(f"✅ Courrier arrivé ajouté: {courrier['id']} - {courrier['objet']}")
            else:
                print(f"❌ Erreur pour {courrier['id']}: {response.text}")
                
        except Exception as e:
            print(f"❌ Erreur pour {courrier['id']}: {e}")

def generer_courriers_envoyes():
    """Générer des courriers envoyés avec des IDs propres"""
    print("\n📤 Génération des courriers envoyés...")
    
    # Date de base pour les courriers envoyés (après les arrivés)
    date_base = date(2024, 1, 20)
    
    courriers_envoyes = [
        {
            "id": "2001",
            "numero_ecrit": "ENV-2024-001",
            "division_emettrice": "technique",
            "date_depart": (date_base + timedelta(days=0)).strftime('%Y-%m-%d'),
            "nature": "message",
            "objet": "Réponse - Application de gestion validée",
            "destinataire": "1° Bureau",
            "observations": "Application approuvée et en cours de déploiement"
        },
        {
            "id": "2002",
            "numero_ecrit": "ENV-2024-002",
            "division_emettrice": "informatique",
            "date_depart": (date_base + timedelta(days=1)).strftime('%Y-%m-%d'),
            "nature": "nds",
            "objet": "Rapport de test - Système opérationnel",
            "destinataire": "2° Bureau",
            "observations": "Tests concluants, déploiement autorisé"
        },
        {
            "id": "2003",
            "numero_ecrit": "ENV-2024-003",
            "division_emettrice": "planification",
            "date_depart": (date_base + timedelta(days=2)).strftime('%Y-%m-%d'),
            "nature": "decision",
            "objet": "Validation du projet de modernisation",
            "destinataire": "3° Bureau",
            "observations": "Projet approuvé, budget alloué"
        },
        {
            "id": "2004",
            "numero_ecrit": "ENV-2024-004",
            "division_emettrice": "informatique",
            "date_depart": (date_base + timedelta(days=3)).strftime('%Y-%m-%d'),
            "nature": "note_royale",
            "objet": "Accusé de réception - Directive sécurité",
            "destinataire": "4° Bureau",
            "observations": "Directive reçue et mise en application"
        },
        {
            "id": "2005",
            "numero_ecrit": "ENV-2024-005",
            "division_emettrice": "instruction",
            "date_depart": (date_base + timedelta(days=4)).strftime('%Y-%m-%d'),
            "nature": "message",
            "objet": "Programme de formation établi",
            "destinataire": "5° Bureau",
            "observations": "Formation programmée du 5 au 9 février 2024"
        },
        {
            "id": "2006",
            "numero_ecrit": "ENV-2024-006",
            "division_emettrice": "rh",
            "date_depart": (date_base + timedelta(days=5)).strftime('%Y-%m-%d'),
            "nature": "message",
            "objet": "Gestion des ressources humaines - Nouvelle procédure",
            "destinataire": "6° Bureau",
            "observations": "Procédure RH mise à jour selon les nouvelles directives"
        },
        {
            "id": "2007",
            "numero_ecrit": "ENV-2024-007",
            "division_emettrice": "mcpo",
            "date_depart": (date_base + timedelta(days=6)).strftime('%Y-%m-%d'),
            "nature": "nds",
            "objet": "Rapport de maintenance - Janvier 2024",
            "destinataire": "7° Bureau",
            "observations": "Maintenance préventive effectuée, systèmes opérationnels"
        }
    ]
    
    for courrier in courriers_envoyes:
        try:
            response = requests.post(
                f"{BASE_URL}/api/courriers/envoyes",
                headers={"Content-Type": "application/json"},
                data=json.dumps(courrier)
            )
            
            if response.status_code == 201:
                print(f"✅ Courrier envoyé ajouté: {courrier['id']} - {courrier['objet']}")
            else:
                print(f"❌ Erreur pour {courrier['id']}: {response.text}")
                
        except Exception as e:
            print(f"❌ Erreur pour {courrier['id']}: {e}")

def verifier_donnees_finales():
    """Vérifier les données finales générées"""
    print("\n🔍 Vérification des données finales...")
    
    try:
        # Vérifier courriers arrivés
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response.status_code == 200:
            courriers_arrives = response.json()
            print(f"📥 Courriers arrivés: {len(courriers_arrives)}")
            for courrier in courriers_arrives:
                print(f"   • {courrier['id']}: {courrier['objet']}")
        
        # Vérifier courriers envoyés
        response = requests.get(f"{BASE_URL}/api/courriers/envoyes")
        if response.status_code == 200:
            courriers_envoyes = response.json()
            print(f"📤 Courriers envoyés: {len(courriers_envoyes)}")
            for courrier in courriers_envoyes:
                print(f"   • {courrier['id']}: {courrier['objet']}")
                
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")

def main():
    """Fonction principale"""
    print("🔄 RÉGÉNÉRATION COMPLÈTE DES DONNÉES DE TEST")
    print("=" * 60)
    
    # Vérifier que l'application Flask fonctionne
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print(f"❌ Application Flask non accessible sur {BASE_URL}")
            return
    except Exception as e:
        print(f"❌ Impossible de se connecter à Flask: {e}")
        return
    
    print(f"✅ Application Flask accessible")
    
    # Étapes de régénération
    supprimer_toutes_donnees()
    generer_courriers_arrives()
    generer_courriers_envoyes()
    verifier_donnees_finales()
    
    print("\n" + "=" * 60)
    print("🎉 RÉGÉNÉRATION TERMINÉE")
    print("=" * 60)
    
    print("\n📊 NOUVELLES DONNÉES GÉNÉRÉES:")
    print("📥 Courriers Arrivés (IDs 1001-1005):")
    print("   • 1001 - Demande d'application de gestion")
    print("   • 1002 - Test de déploiement du système")
    print("   • 1003 - Projet de modernisation")
    print("   • 1004 - Directive sécurité (Note Royale)")
    print("   • 1005 - Demande de formation")
    
    print("\n📤 Courriers Envoyés (IDs 2001-2007):")
    print("   • 2001 - Réponse application validée")
    print("   • 2002 - Rapport de test système")
    print("   • 2003 - Validation projet modernisation")
    print("   • 2004 - Accusé réception directive")
    print("   • 2005 - Programme de formation")
    print("   • 2006 - Nouvelle procédure RH")
    print("   • 2007 - Rapport de maintenance")
    
    print("\n🌐 Vérifiez dans l'interface web:")
    print(f"   📥 Courriers arrivés: {BASE_URL}/courrier/arrives")
    print(f"   📤 Courriers envoyés: {BASE_URL}/courrier/envoyes")
    
    print("\n✨ CARACTÉRISTIQUES DES NOUVELLES DONNÉES:")
    print("   ✅ IDs propres et cohérents (1001-1005, 2001-2007)")
    print("   ✅ Numéros d'écrits structurés (MSG-2024-001, ENV-2024-001)")
    print("   ✅ Dates logiques et séquentielles")
    print("   ✅ Objets réalistes et variés")
    print("   ✅ Classifications et urgences diversifiées")
    print("   ✅ Divisions d'action et d'information assignées")

if __name__ == "__main__":
    main()
