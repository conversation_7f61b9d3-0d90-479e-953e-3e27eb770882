{% extends "rh/base_rh.html" %}

{% block title %}Gestion des Courriers RH - RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-envelope"></i>
                                Gestion des Courriers RH
                            </h2>
                            <small class="text-muted">Suivi des courriers d'arrivée et de départ</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouveau_courrier') }}" class="btn btn-success-military">
                                <i class="fas fa-plus"></i> Nouveau Courrier
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Onglets Type de Courrier -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-body">
                    <ul class="nav nav-pills nav-military justify-content-center">
                        <li class="nav-item">
                            <a class="nav-link {% if type_filter == 'arrivee' %}active{% endif %}" 
                               href="{{ url_for('rh.liste_courriers', type='arrivee', search=search) }}">
                                <i class="fas fa-inbox"></i> Courriers d'Arrivée
                                <span class="badge badge-light ms-2">
                                    {% if type_filter == 'arrivee' %}{{ courriers.total }}{% else %}0{% endif %}
                                </span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if type_filter == 'depart' %}active{% endif %}" 
                               href="{{ url_for('rh.liste_courriers', type='depart', search=search) }}">
                                <i class="fas fa-paper-plane"></i> Courriers de Départ
                                <span class="badge badge-light ms-2">
                                    {% if type_filter == 'depart' %}{{ courriers.total }}{% else %}0{% endif %}
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Filtres et Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="type" value="{{ type_filter }}">
                        <div class="col-md-8">
                            <label class="form-label-military">Recherche</label>
                            <input type="text" name="search" class="form-control form-control-military" 
                                   placeholder="Objet, référence, expéditeur/destinataire..." value="{{ search }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label-military">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-military">
                                    <i class="fas fa-search"></i> Rechercher
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-envelope stat-icon"></i>
                <div class="stat-number">{{ courriers.total }}</div>
                <div class="stat-label">Total Courriers</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-inbox stat-icon"></i>
                <div class="stat-number">{{ courriers.items|selectattr('priorite', 'equalto', 'Urgente')|list|length }}</div>
                <div class="stat-label">Urgents</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-calendar stat-icon"></i>
                <div class="stat-number">{{ courriers.items|selectattr('date_courrier')|selectattr('date_courrier', 'equalto', date.today())|list|length }}</div>
                <div class="stat-label">Aujourd'hui</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-chart-line stat-icon"></i>
                <div class="stat-number">{{ ((courriers.items|length / 20) * 100)|round|int }}%</div>
                <div class="stat-label">Taux de Traitement</div>
            </div>
        </div>
    </div>

    <!-- Liste des Courriers -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                {% if type_filter == 'arrivee' %}Courriers d'Arrivée{% else %}Courriers de Départ{% endif %}
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group">
                                <button class="btn btn-info-military btn-sm" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button class="btn btn-info-military btn-sm" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                                <button class="btn btn-warning-military btn-sm" onclick="printList()">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if courriers.items %}
                    <div class="table-responsive">
                        <table class="table table-military mb-0">
                            <thead>
                                <tr>
                                    <th>Référence</th>
                                    <th>Objet</th>
                                    {% if type_filter == 'arrivee' %}
                                    <th>Expéditeur</th>
                                    <th>Date Réception</th>
                                    {% else %}
                                    <th>Destinataire</th>
                                    <th>Date Envoi</th>
                                    {% endif %}
                                    <th>Date Courrier</th>
                                    <th>Priorité</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for courrier in courriers.items %}
                                <tr>
                                    <td>
                                        <strong>{{ courrier.reference_courrier or 'N/A' }}</strong>
                                    </td>
                                    <td>
                                        <div title="{{ courrier.objet_courrier or 'Aucun objet' }}">
                                            {{ (courrier.objet_courrier or 'Aucun objet')[:50] }}{% if courrier.objet_courrier and courrier.objet_courrier|length > 50 %}...{% endif %}
                                        </div>
                                    </td>
                                    {% if type_filter == 'arrivee' %}
                                    <td>{{ courrier.expediteur or 'N/A' }}</td>
                                    <td>
                                        {% if courrier.date_reception %}
                                        {{ courrier.date_reception.strftime('%d/%m/%Y') }}
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    {% else %}
                                    <td>{{ courrier.destinataire or 'N/A' }}</td>
                                    <td>
                                        {% if courrier.date_envoi %}
                                        {{ courrier.date_envoi.strftime('%d/%m/%Y') }}
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    <td>
                                        {% if courrier.date_courrier %}
                                        {{ courrier.date_courrier.strftime('%d/%m/%Y') }}
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if courrier.priorite == 'Très urgente' %}
                                        <span class="badge badge-danger-military">
                                            <i class="fas fa-exclamation-triangle"></i> {{ courrier.priorite }}
                                        </span>
                                        {% elif courrier.priorite == 'Urgente' %}
                                        <span class="badge badge-warning-military">
                                            <i class="fas fa-clock"></i> {{ courrier.priorite }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-info-military">
                                            <i class="fas fa-info-circle"></i> {{ courrier.priorite or 'Normale' }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-info-military btn-sm" 
                                                    onclick="voirCourrier({{ courrier.id }})" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-warning-military btn-sm" 
                                                    onclick="modifierCourrier({{ courrier.id }})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-success-military btn-sm" 
                                                    onclick="archiverCourrier({{ courrier.id }})" title="Archiver">
                                                <i class="fas fa-archive"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun courrier trouvé</h5>
                        <p class="text-muted">Aucun courrier ne correspond aux critères de recherche.</p>
                        <a href="{{ url_for('rh.nouveau_courrier') }}" class="btn btn-success-military">
                            <i class="fas fa-plus"></i> Créer un Courrier
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Pagination -->
                {% if courriers.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination justify-content-center mb-0">
                            {% if courriers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_courriers', page=courriers.prev_num, search=search, type=type_filter) }}">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in courriers.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != courriers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('rh.liste_courriers', page=page_num, search=search, type=type_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if courriers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_courriers', page=courriers.next_num, search=search, type=type_filter) }}">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.nav-military .nav-link {
    color: var(--text-light);
    border: 1px solid transparent;
    border-radius: 25px;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.nav-military .nav-link:hover {
    color: var(--accent-color);
    background: rgba(255, 213, 79, 0.1);
    border-color: var(--accent-color);
}

.nav-military .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--accent-color);
    border-color: var(--accent-color);
    box-shadow: 0 4px 15px rgba(255, 213, 79, 0.3);
}

.badge-light {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-light);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function voirCourrier(id) {
    alert('Détails du courrier #' + id + ' - Fonctionnalité à développer');
}

function modifierCourrier(id) {
    window.location.href = `/rh/courriers/${id}/modifier`;
}

function archiverCourrier(id) {
    if (confirm('Êtes-vous sûr de vouloir archiver ce courrier ?')) {
        alert('Courrier #' + id + ' archivé - Fonctionnalité à développer');
        location.reload();
    }
}

function exportToExcel() {
    alert('Export Excel - Fonctionnalité à développer');
}

function exportToPDF() {
    alert('Export PDF - Fonctionnalité à développer');
}

function printList() {
    window.print();
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
