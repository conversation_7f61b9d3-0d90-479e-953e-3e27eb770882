"""
Routes pour l'application RH militaire
Division des Ressources Humaines - Inspection de l'Artillerie
Forces Armées Royales
"""

from flask import render_template, request, redirect, url_for, flash, jsonify
from . import rh_bp
from .models import *
from db import db
from datetime import datetime, date
from sqlalchemy import or_, and_, func

@rh_bp.route('/')
def dashboard():
    """Dashboard principal de l'application RH"""
    try:
        # Statistiques générales
        total_personnel = Personnel.query.count()
        
        # Statistiques par catégorie
        stats_categories = db.session.query(
            ReferentielCategorie.libelle,
            func.count(Personnel.matricule).label('count')
        ).join(Personnel).group_by(ReferentielCategorie.libelle).all()
        
        # Statistiques par grade (top 5)
        stats_grades = db.session.query(
            ReferentielGrade.libelle,
            func.count(Personnel.matricule).label('count')
        ).join(Personnel).group_by(ReferentielGrade.libelle).order_by(func.count(Personnel.matricule).desc()).limit(5).all()
        
        # Statistiques par unité (top 5)
        stats_unites = db.session.query(
            ReferentielUnite.libelle,
            func.count(Personnel.matricule).label('count')
        ).join(Personnel).group_by(ReferentielUnite.libelle).order_by(func.count(Personnel.matricule).desc()).limit(5).all()
        
        # Statistiques médicales
        total_aptes = SituationMedicale.query.filter_by(aptitude='Apte').count()
        total_inaptes = SituationMedicale.query.filter_by(aptitude='Inapte').count()
        
        # Statistiques d'absences récentes (30 derniers jours)
        from datetime import timedelta
        date_limite = date.today() - timedelta(days=30)
        
        permissions_recentes = Permission.query.filter(Permission.date_debut >= date_limite).count()
        detachements_en_cours = Detachement.query.filter(Detachement.date_fin.is_(None)).count()
        
        # Alertes
        alertes = []
        
        # Alertes CIN expirées ou à expirer dans 30 jours
        date_alerte_cin = date.today() + timedelta(days=30)
        cin_expires = Personnel.query.filter(Personnel.cin_date_expiration <= date_alerte_cin).count()
        if cin_expires > 0:
            alertes.append({
                'type': 'warning',
                'message': f'{cin_expires} CIN expirée(s) ou à expirer dans 30 jours',
                'url': url_for('rh.recherche_personnel') + '?alerte=cin'
            })
        
        # Alertes désertions en cours
        desertions_en_cours = Desertion.query.filter(Desertion.date_retour.is_(None)).count()
        if desertions_en_cours > 0:
            alertes.append({
                'type': 'danger',
                'message': f'{desertions_en_cours} désertion(s) en cours',
                'url': url_for('rh.gestion_absences') + '?type=desertion'
            })
        
        return render_template('rh/dashboard.html',
                             total_personnel=total_personnel,
                             stats_categories=stats_categories,
                             stats_grades=stats_grades,
                             stats_unites=stats_unites,
                             total_aptes=total_aptes,
                             total_inaptes=total_inaptes,
                             permissions_recentes=permissions_recentes,
                             detachements_en_cours=detachements_en_cours,
                             alertes=alertes)
    
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('rh/dashboard.html',
                             total_personnel=0,
                             stats_categories=[],
                             stats_grades=[],
                             stats_unites=[],
                             total_aptes=0,
                             total_inaptes=0,
                             permissions_recentes=0,
                             detachements_en_cours=0,
                             alertes=[])

@rh_bp.route('/recherche')
def recherche_personnel():
    """Page de recherche et consultation du personnel"""
    try:
        # Paramètres de recherche
        search_term = request.args.get('search', '').strip()
        matricule = request.args.get('matricule', '').strip()
        cin = request.args.get('cin', '').strip()
        gsm = request.args.get('gsm', '').strip()
        categorie_id = request.args.get('categorie_id', '')
        grade_id = request.args.get('grade_id', '')
        service_id = request.args.get('service_id', '')
        unite_id = request.args.get('unite_id', '')
        genre_id = request.args.get('genre_id', '')
        etat_matrimonial_id = request.args.get('etat_matrimonial_id', '')
        aptitude = request.args.get('aptitude', '').strip()
        lieu_naissance = request.args.get('lieu_naissance', '').strip()
        alerte = request.args.get('alerte', '')

        # NOUVEAUX CRITÈRES AVANCÉS
        tranche_age = request.args.get('tranche_age', '').strip()
        anciennete = request.args.get('anciennete', '').strip()
        tranche_taille = request.args.get('tranche_taille', '').strip()
        fonction = request.args.get('fonction', '').strip()
        statut_sanctions = request.args.get('statut_sanctions', '').strip()
        statut_absences = request.args.get('statut_absences', '').strip()
        statut_detachements = request.args.get('statut_detachements', '').strip()
        statut_mutations = request.args.get('statut_mutations', '').strip()
        statut_medical = request.args.get('statut_medical', '').strip()
        statut_famille = request.args.get('statut_famille', '').strip()
        statut_carriere = request.args.get('statut_carriere', '').strip()
        statut_operationnel = request.args.get('statut_operationnel', '').strip()
        statut_documents = request.args.get('statut_documents', '').strip()
        type_alerte = request.args.get('type_alerte', '').strip()
        indicateur_performance = request.args.get('indicateur_performance', '').strip()
        
        # Construction de la requête
        query = Personnel.query
        
        # Filtres de recherche - AMÉLIORATION : nom ET/OU prénom
        if search_term:
            # Diviser le terme de recherche en mots
            mots = search_term.strip().split()

            if len(mots) == 1:
                # Un seul mot : rechercher dans nom OU prénom
                mot = mots[0]
                query = query.filter(
                    or_(
                        Personnel.nom.contains(mot),
                        Personnel.prenom.contains(mot),
                        Personnel.nom_ar.contains(mot),
                        Personnel.prenom_ar.contains(mot)
                    )
                )
            else:
                # Plusieurs mots : rechercher nom ET prénom ensemble
                conditions = []
                for mot in mots:
                    conditions.append(
                        or_(
                            Personnel.nom.contains(mot),
                            Personnel.prenom.contains(mot),
                            Personnel.nom_ar.contains(mot),
                            Personnel.prenom_ar.contains(mot)
                        )
                    )
                # Tous les mots doivent être trouvés (AND)
                query = query.filter(and_(*conditions))
        
        if matricule:
            query = query.filter(Personnel.matricule.contains(matricule))

        if cin:
            query = query.filter(Personnel.cin_numero.contains(cin))

        if gsm:
            query = query.filter(
                or_(
                    Personnel.gsm.contains(gsm),
                    Personnel.gsm_urgence.contains(gsm),
                    Personnel.telephone_domicile.contains(gsm)
                )
            )

        if categorie_id:
            query = query.filter(Personnel.categorie_id == int(categorie_id))

        if grade_id:
            query = query.filter(Personnel.grade_actuel_id == int(grade_id))

        if service_id:
            query = query.filter(Personnel.service_id == int(service_id))

        if unite_id:
            query = query.filter(Personnel.unite_id == int(unite_id))

        if genre_id:
            query = query.filter(Personnel.genre_id == int(genre_id))

        if etat_matrimonial_id:
            query = query.filter(Personnel.etat_matrimonial_id == int(etat_matrimonial_id))

        if lieu_naissance:
            query = query.filter(Personnel.lieu_naissance.contains(lieu_naissance))

        if aptitude:
            # Filtre par aptitude médicale via sous-requête
            sous_query = SituationMedicale.query.filter(
                SituationMedicale.aptitude == aptitude
            ).with_entities(SituationMedicale.matricule).subquery()
            query = query.filter(Personnel.matricule.in_(sous_query))
        
        # Alerte CIN
        if alerte == 'cin':
            from datetime import timedelta
            date_alerte = date.today() + timedelta(days=30)
            query = query.filter(Personnel.cin_date_expiration <= date_alerte)

        # FILTRES AVANCÉS LOGIQUES ET CRÉATIFS

        # Filtre par tranche d'âge
        if tranche_age:
            today = date.today()
            if tranche_age == '18-25':
                date_min = today.replace(year=today.year - 25)
                date_max = today.replace(year=today.year - 18)
                query = query.filter(Personnel.date_naissance.between(date_min, date_max))
            elif tranche_age == '26-35':
                date_min = today.replace(year=today.year - 35)
                date_max = today.replace(year=today.year - 26)
                query = query.filter(Personnel.date_naissance.between(date_min, date_max))
            elif tranche_age == '36-45':
                date_min = today.replace(year=today.year - 45)
                date_max = today.replace(year=today.year - 36)
                query = query.filter(Personnel.date_naissance.between(date_min, date_max))
            elif tranche_age == '46-55':
                date_min = today.replace(year=today.year - 55)
                date_max = today.replace(year=today.year - 46)
                query = query.filter(Personnel.date_naissance.between(date_min, date_max))
            elif tranche_age == '56+':
                date_max = today.replace(year=today.year - 56)
                query = query.filter(Personnel.date_naissance <= date_max)

        # Filtre par fonction
        if fonction:
            query = query.filter(Personnel.fonction.contains(fonction))

        # Filtre par tranche de taille
        if tranche_taille:
            if tranche_taille == '150-160':
                query = query.filter(Personnel.taille_cm.between(150, 160))
            elif tranche_taille == '161-170':
                query = query.filter(Personnel.taille_cm.between(161, 170))
            elif tranche_taille == '171-180':
                query = query.filter(Personnel.taille_cm.between(171, 180))
            elif tranche_taille == '181-190':
                query = query.filter(Personnel.taille_cm.between(181, 190))
            elif tranche_taille == '191+':
                query = query.filter(Personnel.taille_cm >= 191)

        # FILTRES DISCIPLINAIRES ET OPÉRATIONNELS AVANCÉS

        # Filtre par statut sanctions
        if statut_sanctions:
            if statut_sanctions == 'aucune':
                # Militaires sans aucune sanction
                sous_query = Sanction.query.with_entities(Sanction.matricule).subquery()
                query = query.filter(~Personnel.matricule.in_(sous_query))
            elif statut_sanctions == 'en_cours':
                # Sanctions en cours (sans date de fin)
                sous_query = Sanction.query.filter(
                    Sanction.date_fin.is_(None)
                ).with_entities(Sanction.matricule).subquery()
                query = query.filter(Personnel.matricule.in_(sous_query))
            elif statut_sanctions == 'historique':
                # Militaires avec historique de sanctions
                sous_query = Sanction.query.with_entities(Sanction.matricule).subquery()
                query = query.filter(Personnel.matricule.in_(sous_query))
            elif statut_sanctions == 'recidiviste':
                # Récidivistes (2+ sanctions)
                from sqlalchemy import func
                sous_query = db.session.query(Sanction.matricule).group_by(
                    Sanction.matricule
                ).having(func.count(Sanction.id_sanction) >= 2).subquery()
                query = query.filter(Personnel.matricule.in_(sous_query))

        # Filtre par statut absences
        if statut_absences:
            today = date.today()
            if statut_absences == 'en_permission':
                # En permission actuellement
                sous_query = Permission.query.filter(
                    Permission.date_debut <= today,
                    Permission.date_fin >= today
                ).with_entities(Permission.matricule).subquery()
                query = query.filter(Personnel.matricule.in_(sous_query))
            elif statut_absences == 'en_ptc':
                # En PTC actuellement
                sous_query = PTC.query.filter(
                    PTC.date_debut <= today,
                    PTC.date_fin >= today
                ).with_entities(PTC.matricule).subquery()
                query = query.filter(Personnel.matricule.in_(sous_query))
            elif statut_absences == 'retour_recent':
                # Retour récent (7 derniers jours)
                date_limite = today - timedelta(days=7)
                sous_query = Permission.query.filter(
                    Permission.date_fin >= date_limite,
                    Permission.date_fin <= today
                ).with_entities(Permission.matricule).subquery()
                query = query.filter(Personnel.matricule.in_(sous_query))

        # Filtre par statut détachements
        if statut_detachements:
            if statut_detachements == 'en_detachement':
                # En détachement actuellement
                sous_query = Detachement.query.filter(
                    Detachement.date_debut <= today,
                    or_(Detachement.date_fin.is_(None), Detachement.date_fin >= today)
                ).with_entities(Detachement.matricule).subquery()
                query = query.filter(Personnel.matricule.in_(sous_query))
            elif statut_detachements == 'jamais_detache':
                # Jamais détaché
                sous_query = Detachement.query.with_entities(Detachement.matricule).subquery()
                query = query.filter(~Personnel.matricule.in_(sous_query))
        
        # Pagination
        page = request.args.get('page', 1, type=int)
        personnel = query.order_by(Personnel.nom, Personnel.prenom).paginate(
            page=page, per_page=20, error_out=False
        )
        
        # Données pour les filtres - ORDRE EXACT DEMANDÉ
        categories = ReferentielCategorie.query.order_by(ReferentielCategorie.libelle).all()

        # GRADES - UNIQUEMENT LES 14 DEMANDÉS DANS L'ORDRE EXACT
        grades_codes_ordre = ['SOL1', 'SOL2', 'BRG', 'BRGC', 'MDL', 'MDLC', 'ADJ', 'ADJC', 'SLT', 'LTN', 'CPT', 'CDT', 'LCL', 'COL']
        grades = []
        for code in grades_codes_ordre:
            grade = ReferentielGrade.query.filter_by(code_grade=code).first()
            if grade:
                grades.append(grade)

        services = ReferentielService.query.order_by(ReferentielService.libelle).all()

        # UNITÉS - ORDRE EXACT : 1GAR → 26GAR puis autres
        unites = []
        # D'abord 1GAR à 26GAR
        for i in range(1, 27):
            unite = ReferentielUnite.query.filter_by(code=f'{i}GAR').first()
            if unite:
                unites.append(unite)

        # Puis les autres dans l'ordre exact
        autres_codes = ['INSPART', 'ERART', 'GSA', 'CFA', '1BUR', '2BUR', '3BUR', '4BUR', '5BUR',
                       'BREC', 'BCOUR', 'DPO', 'PCA', 'EMZS', 'EMZE', 'SOPTAF', 'SOPSAG', 'SORIENT', 'AUTRE']
        for code in autres_codes:
            unite = ReferentielUnite.query.filter_by(code=code).first()
            if unite:
                unites.append(unite)

        genres = ReferentielGenre.query.order_by(ReferentielGenre.libelle).all()
        etats_matrimoniaux = ReferentielEtatMatrimonial.query.order_by(ReferentielEtatMatrimonial.libelle).all()

        return render_template('rh/recherche_personnel.html',
                             personnel=personnel,
                             categories=categories,
                             grades=grades,
                             services=services,
                             unites=unites,
                             genres=genres,
                             etats_matrimoniaux=etats_matrimoniaux,
                             search_term=search_term,
                             matricule=matricule,
                             cin=cin,
                             gsm=gsm,
                             categorie_id=int(categorie_id) if categorie_id else None,
                             grade_id=int(grade_id) if grade_id else None,
                             service_id=int(service_id) if service_id else None,
                             unite_id=int(unite_id) if unite_id else None,
                             genre_id=int(genre_id) if genre_id else None,
                             etat_matrimonial_id=int(etat_matrimonial_id) if etat_matrimonial_id else None,
                             aptitude=aptitude,
                             lieu_naissance=lieu_naissance,
                             alerte=alerte,
                             date=date)
    
    except Exception as e:
        flash(f'Erreur lors de la recherche: {str(e)}', 'error')
        # En cas d'erreur, charger quand même les données de référence DANS L'ORDRE EXACT
        categories = ReferentielCategorie.query.order_by(ReferentielCategorie.libelle).all()

        # GRADES - ORDRE EXACT
        grades_codes_ordre = ['SOL1', 'SOL2', 'BRG', 'BRGC', 'MDL', 'MDLC', 'ADJ', 'ADJC', 'SLT', 'LTN', 'CPT', 'CDT', 'LCL', 'COL']
        grades = []
        for code in grades_codes_ordre:
            grade = ReferentielGrade.query.filter_by(code_grade=code).first()
            if grade:
                grades.append(grade)

        services = ReferentielService.query.order_by(ReferentielService.libelle).all()

        # UNITÉS - ORDRE EXACT
        unites = []
        for i in range(1, 27):
            unite = ReferentielUnite.query.filter_by(code=f'{i}GAR').first()
            if unite:
                unites.append(unite)
        autres_codes = ['INSPART', 'ERART', 'GSA', 'CFA', '1BUR', '2BUR', '3BUR', '4BUR', '5BUR',
                       'BREC', 'BCOUR', 'DPO', 'PCA', 'EMZS', 'EMZE', 'SOPTAF', 'SOPSAG', 'SORIENT', 'AUTRE']
        for code in autres_codes:
            unite = ReferentielUnite.query.filter_by(code=code).first()
            if unite:
                unites.append(unite)

        genres = ReferentielGenre.query.order_by(ReferentielGenre.libelle).all()
        etats_matrimoniaux = ReferentielEtatMatrimonial.query.order_by(ReferentielEtatMatrimonial.libelle).all()

        return render_template('rh/recherche_personnel.html',
                             personnel=None,
                             categories=categories,
                             grades=grades,
                             services=services,
                             unites=unites,
                             genres=genres,
                             etats_matrimoniaux=etats_matrimoniaux,
                             search_term='',
                             matricule='',
                             cin='',
                             gsm='',
                             categorie_id=None,
                             grade_id=None,
                             service_id=None,
                             unite_id=None,
                             genre_id=None,
                             etat_matrimonial_id=None,
                             aptitude='',
                             lieu_naissance='',
                             alerte='')

@rh_bp.route('/personnel/<matricule>')
def fiche_personnel(matricule):
    """Affichage de la fiche complète d'un militaire avec tous les aspects"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        # Données médicales
        situation_medicale = SituationMedicale.query.filter_by(matricule=matricule).first()
        hospitalisations = Hospitalisation.query.filter_by(matricule=matricule).order_by(Hospitalisation.date_entree.desc()).limit(5).all()
        vaccinations = Vaccination.query.filter_by(matricule=matricule).order_by(Vaccination.date_vaccination.desc()).limit(5).all()
        ptcs = PTC.query.filter_by(matricule=matricule).order_by(PTC.date_debut.desc()).limit(5).all()

        # Données familiales
        conjoint = Conjoint.query.filter_by(matricule=matricule).first()
        enfants = Enfant.query.filter_by(matricule=matricule).order_by(Enfant.date_naissance).all()

        # Absences récentes
        permissions = Permission.query.filter_by(matricule=matricule).order_by(Permission.date_debut.desc()).limit(5).all()
        desertions = Desertion.query.filter_by(matricule=matricule).order_by(Desertion.date_absence.desc()).limit(3).all()
        detachements = Detachement.query.filter_by(matricule=matricule).order_by(Detachement.date_debut.desc()).limit(3).all()

        # Mouvements récents
        mutations = MutationInterBie.query.filter_by(matricule=matricule).order_by(MutationInterBie.date_mutation.desc()).limit(5).all()
        sejours_ops = SejourOperationnel.query.filter_by(matricule=matricule).order_by(SejourOperationnel.date_debut.desc()).limit(3).all()
        liberations = Liberation.query.filter_by(matricule=matricule).order_by(Liberation.date_liberation.desc()).limit(3).all()
        sanctions = Sanction.query.filter_by(matricule=matricule).order_by(Sanction.date_sanction.desc()).limit(3).all()

        # Historique des grades
        historique_grades = HistoriqueGrade.query.filter_by(matricule=matricule).order_by(HistoriqueGrade.date_debut.desc()).all()

        # Langues parlées
        langues = PersonnelLangue.query.filter_by(matricule=matricule).all()

        # Calculs statistiques
        total_permissions = Permission.query.filter_by(matricule=matricule).count()
        total_hospitalisations = Hospitalisation.query.filter_by(matricule=matricule).count()
        total_mutations = MutationInterBie.query.filter_by(matricule=matricule).count()

        # Alertes personnalisées
        alertes = []

        # Alerte CIN
        if militaire.cin_date_expiration:
            from datetime import timedelta
            jours_restants = (militaire.cin_date_expiration - date.today()).days
            if jours_restants <= 30:
                alertes.append({
                    'type': 'warning' if jours_restants > 0 else 'danger',
                    'message': f'CIN expire {"dans " + str(jours_restants) + " jours" if jours_restants > 0 else "expirée"}',
                    'icon': 'id-card'
                })

        # Alerte désertion en cours
        desertion_en_cours = Desertion.query.filter_by(matricule=matricule, date_retour=None).first()
        if desertion_en_cours:
            alertes.append({
                'type': 'danger',
                'message': f'Désertion en cours depuis le {desertion_en_cours.date_absence.strftime("%d/%m/%Y")}',
                'icon': 'exclamation-triangle'
            })

        # Alerte détachement en cours
        detachement_en_cours = Detachement.query.filter_by(matricule=matricule, date_fin=None).first()
        if detachement_en_cours:
            alertes.append({
                'type': 'info',
                'message': f'En détachement à {detachement_en_cours.lieu_detachement}',
                'icon': 'plane'
            })

        return render_template('rh/fiche_personnel_complete.html',
                             militaire=militaire,
                             situation_medicale=situation_medicale,
                             hospitalisations=hospitalisations,
                             vaccinations=vaccinations,
                             ptcs=ptcs,
                             conjoint=conjoint,
                             enfants=enfants,
                             permissions=permissions,
                             desertions=desertions,
                             detachements=detachements,
                             mutations=mutations,
                             sejours_ops=sejours_ops,
                             liberations=liberations,
                             sanctions=sanctions,
                             historique_grades=historique_grades,
                             langues=langues,
                             total_permissions=total_permissions,
                             total_hospitalisations=total_hospitalisations,
                             total_mutations=total_mutations,
                             alertes=alertes,
                             date=date)

    except Exception as e:
        flash(f'Erreur lors du chargement de la fiche: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/nouveau_militaire', methods=['GET', 'POST'])
def nouveau_militaire():
    """Formulaire d'ajout d'un nouveau militaire"""
    if request.method == 'POST':
        try:
            # Création du nouveau militaire
            militaire = Personnel(
                matricule=request.form['matricule'],
                nom=request.form['nom'],
                prenom=request.form['prenom'],
                nom_ar=request.form['nom_ar'],
                prenom_ar=request.form['prenom_ar'],
                date_naissance=datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date(),
                lieu_naissance=request.form['lieu_naissance'],
                genre_id=int(request.form['genre_id']),
                categorie_id=int(request.form['categorie_id']),
                groupe_sanguin_id=int(request.form['groupe_sanguin_id']),
                cin_numero=request.form['cin_numero'],
                cin_date_delivrance=datetime.strptime(request.form['cin_date_delivrance'], '%Y-%m-%d').date(),
                cin_date_expiration=datetime.strptime(request.form['cin_date_expiration'], '%Y-%m-%d').date(),
                gsm=request.form['gsm'],
                telephone_domicile=request.form.get('telephone_domicile'),
                taille_cm=int(request.form['taille_cm']),
                lieu_residence=request.form['lieu_residence'],
                service_id=int(request.form['service_id']),
                specialite_id=int(request.form['specialite_id']) if request.form.get('specialite_id') else None,
                unite_id=int(request.form['unite_id']),
                grade_actuel_id=int(request.form['grade_actuel_id']),
                fonction=request.form['fonction'],
                date_prise_fonction=datetime.strptime(request.form['date_prise_fonction'], '%Y-%m-%d').date(),
                date_engagement=datetime.strptime(request.form['date_engagement'], '%Y-%m-%d').date(),
                ccp_numero=request.form['ccp_numero'],
                compte_bancaire_numero=request.form.get('compte_bancaire_numero'),
                somme_numero=request.form['somme_numero'],
                nom_pere=request.form['nom_pere'],
                prenom_pere=request.form['prenom_pere'],
                nom_mere=request.form['nom_mere'],
                prenom_mere=request.form['prenom_mere'],
                adresse_parents=request.form['adresse_parents'],
                etat_matrimonial_id=int(request.form['etat_matrimonial_id']),
                nombre_enfants=int(request.form['nombre_enfants']) if request.form.get('nombre_enfants') else None,
                passeport_numero=request.form.get('passeport_numero'),
                passeport_date_delivrance=datetime.strptime(request.form['passeport_date_delivrance'], '%Y-%m-%d').date() if request.form.get('passeport_date_delivrance') else None,
                passeport_date_expiration=datetime.strptime(request.form['passeport_date_expiration'], '%Y-%m-%d').date() if request.form.get('passeport_date_expiration') else None,
                gsm_urgence=request.form['gsm_urgence'],
                lien_parente_id=int(request.form['lien_parente_id'])
            )
            
            db.session.add(militaire)
            
            # Création de l'historique de grade initial
            historique_grade = HistoriqueGrade(
                matricule=militaire.matricule,
                grade_id=militaire.grade_actuel_id,
                date_debut=militaire.date_engagement,
                numero_decision=f"Engagement {militaire.matricule}",
                observations="Grade initial à l'engagement"
            )
            
            db.session.add(historique_grade)
            
            # Création de la situation médicale initiale
            situation_medicale = SituationMedicale(
                matricule=militaire.matricule,
                aptitude='Apte',
                observations_generales="Situation médicale initiale"
            )
            
            db.session.add(situation_medicale)
            
            db.session.commit()
            
            flash(f'Militaire {militaire.nom_complet} ajouté avec succès!', 'success')
            return redirect(url_for('rh.fiche_personnel', matricule=militaire.matricule))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la création: {str(e)}', 'error')
    
    # Données pour le formulaire
    try:
        genres = ReferentielGenre.query.all()
        categories = ReferentielCategorie.query.all()
        groupes_sanguins = ReferentielGroupeSanguin.query.all()
        services = ReferentielService.query.order_by(ReferentielService.libelle).all()

        # UNITÉS - ORDRE EXACT : 1GAR → 26GAR puis autres
        unites = []
        for i in range(1, 27):
            unite = ReferentielUnite.query.filter_by(code=f'{i}GAR').first()
            if unite:
                unites.append(unite)
        autres_codes = ['INSPART', 'ERART', 'GSA', 'CFA', '1BUR', '2BUR', '3BUR', '4BUR', '5BUR',
                       'BREC', 'BCOUR', 'DPO', 'PCA', 'EMZS', 'EMZE', 'SOPTAF', 'SOPSAG', 'SORIENT', 'AUTRE']
        for code in autres_codes:
            unite = ReferentielUnite.query.filter_by(code=code).first()
            if unite:
                unites.append(unite)

        # GRADES - UNIQUEMENT LES 14 DEMANDÉS DANS L'ORDRE EXACT
        grades_codes_ordre = ['SOL1', 'SOL2', 'BRG', 'BRGC', 'MDL', 'MDLC', 'ADJ', 'ADJC', 'SLT', 'LTN', 'CPT', 'CDT', 'LCL', 'COL']
        grades = []
        for code in grades_codes_ordre:
            grade = ReferentielGrade.query.filter_by(code_grade=code).first()
            if grade:
                grades.append(grade)

        etats_matrimoniaux = ReferentielEtatMatrimonial.query.all()
        liens_parente = ReferentielLienParente.query.all()
        
        return render_template('rh/nouveau_militaire.html',
                             genres=genres,
                             categories=categories,
                             groupes_sanguins=groupes_sanguins,
                             services=services,
                             unites=unites,
                             grades=grades,
                             etats_matrimoniaux=etats_matrimoniaux,
                             liens_parente=liens_parente)
    
    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {str(e)}', 'error')
        return redirect(url_for('rh.dashboard'))

# =============================================================================
# GESTION FAMILIALE
# =============================================================================

@rh_bp.route('/personnel/<matricule>/gestion_medical')
def gestion_medical(matricule):
    """Page de gestion médicale complète"""
    try:
        militaire = Personnel.query.get_or_404(matricule)
        situation_medicale = SituationMedicale.query.filter_by(matricule=matricule).first()
        hospitalisations = Hospitalisation.query.filter_by(matricule=matricule).order_by(Hospitalisation.date_entree.desc()).all()
        vaccinations = Vaccination.query.filter_by(matricule=matricule).order_by(Vaccination.date_vaccination.desc()).all()
        ptcs = PTC.query.filter_by(matricule=matricule).order_by(PTC.date_debut.desc()).all()

        return render_template('rh/section_medicale.html',
                             militaire=militaire,
                             situation_medicale=situation_medicale,
                             hospitalisations=hospitalisations,
                             vaccinations=vaccinations,
                             ptcs=ptcs)
    except Exception as e:
        flash(f'Erreur lors du chargement de la section médicale: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/gestion_permissions')
def gestion_permissions(matricule):
    """Page de gestion des permissions et absences"""
    try:
        militaire = Personnel.query.get_or_404(matricule)
        permissions = Permission.query.filter_by(matricule=matricule).order_by(Permission.date_debut.desc()).all()
        desertions = Desertion.query.filter_by(matricule=matricule).order_by(Desertion.date_absence.desc()).all()
        detachements = Detachement.query.filter_by(matricule=matricule).order_by(Detachement.date_debut.desc()).all()

        return render_template('rh/absences/liste.html',
                             militaire=militaire,
                             permissions=permissions,
                             desertions=desertions,
                             detachements=detachements)
    except Exception as e:
        flash(f'Erreur lors du chargement des absences: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/gestion_mutations')
def gestion_mutations(matricule):
    """Page de gestion des mutations et mouvements"""
    try:
        militaire = Personnel.query.get_or_404(matricule)
        mutations = Mutation.query.filter_by(matricule=matricule).order_by(Mutation.date_mutation.desc()).all()
        sejours_ops = SejourOperationnel.query.filter_by(matricule=matricule).order_by(SejourOperationnel.date_debut.desc()).all()
        liberations = Liberation.query.filter_by(matricule=matricule).order_by(Liberation.date_liberation.desc()).all()
        sanctions = Sanction.query.filter_by(matricule=matricule).order_by(Sanction.date_sanction.desc()).all()

        return render_template('rh/mutations/liste.html',
                             militaire=militaire,
                             mutations=mutations,
                             sejours_ops=sejours_ops,
                             liberations=liberations,
                             sanctions=sanctions)
    except Exception as e:
        flash(f'Erreur lors du chargement des mouvements: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/conjoint', methods=['GET', 'POST'])
def gestion_conjoint(matricule):
    """Gestion du conjoint d'un militaire"""
    try:
        militaire = Personnel.query.get_or_404(matricule)
        conjoint = Conjoint.query.filter_by(matricule=matricule).first()

        if request.method == 'POST':
            if conjoint:
                # Mise à jour du conjoint existant
                conjoint.nom = request.form['nom']
                conjoint.prenom = request.form['prenom']
                conjoint.nom_ar = request.form['nom_ar']
                conjoint.prenom_ar = request.form['prenom_ar']
                conjoint.date_naissance = datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date()
                conjoint.lieu_naissance = request.form['lieu_naissance']
                conjoint.cin_numero = request.form['cin_numero']
                conjoint.cin_date_delivrance = datetime.strptime(request.form['cin_date_delivrance'], '%Y-%m-%d').date()
                conjoint.cin_date_expiration = datetime.strptime(request.form['cin_date_expiration'], '%Y-%m-%d').date()
                conjoint.nom_pere = request.form['nom_pere']
                conjoint.prenom_pere = request.form['prenom_pere']
                conjoint.nom_mere = request.form['nom_mere']
                conjoint.prenom_mere = request.form['prenom_mere']
                conjoint.profession = request.form.get('profession')
                conjoint.lieu_travail = request.form.get('lieu_travail')
                conjoint.gsm = request.form.get('gsm')
                conjoint.date_mariage = datetime.strptime(request.form['date_mariage'], '%Y-%m-%d').date()
                conjoint.lieu_mariage = request.form['lieu_mariage']

                flash('Informations du conjoint mises à jour avec succès!', 'success')
            else:
                # Création d'un nouveau conjoint
                conjoint = Conjoint(
                    matricule=matricule,
                    nom=request.form['nom'],
                    prenom=request.form['prenom'],
                    nom_ar=request.form['nom_ar'],
                    prenom_ar=request.form['prenom_ar'],
                    date_naissance=datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date(),
                    lieu_naissance=request.form['lieu_naissance'],
                    cin_numero=request.form['cin_numero'],
                    cin_date_delivrance=datetime.strptime(request.form['cin_date_delivrance'], '%Y-%m-%d').date(),
                    cin_date_expiration=datetime.strptime(request.form['cin_date_expiration'], '%Y-%m-%d').date(),
                    nom_pere=request.form['nom_pere'],
                    prenom_pere=request.form['prenom_pere'],
                    nom_mere=request.form['nom_mere'],
                    prenom_mere=request.form['prenom_mere'],
                    profession=request.form.get('profession'),
                    lieu_travail=request.form.get('lieu_travail'),
                    gsm=request.form.get('gsm'),
                    date_mariage=datetime.strptime(request.form['date_mariage'], '%Y-%m-%d').date(),
                    lieu_mariage=request.form['lieu_mariage']
                )
                db.session.add(conjoint)
                flash('Conjoint ajouté avec succès!', 'success')

            db.session.commit()
            return redirect(url_for('rh.fiche_personnel', matricule=matricule))

        return render_template('rh/gestion_conjoint.html', militaire=militaire, conjoint=conjoint)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la gestion du conjoint: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/enfants')
def gestion_enfants(matricule):
    """Gestion des enfants d'un militaire"""
    try:
        militaire = Personnel.query.get_or_404(matricule)
        enfants = Enfant.query.filter_by(matricule=matricule).order_by(Enfant.date_naissance).all()
        genres = ReferentielGenre.query.all()

        return render_template('rh/gestion_enfants.html',
                             militaire=militaire,
                             enfants=enfants,
                             genres=genres)

    except Exception as e:
        flash(f'Erreur lors du chargement des enfants: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/enfant/nouveau', methods=['GET', 'POST'])
def nouveau_enfant(matricule):
    """Ajout d'un nouvel enfant"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            enfant = Enfant(
                matricule=matricule,
                nom=request.form['nom'],
                prenom=request.form['prenom'],
                nom_ar=request.form['nom_ar'],
                prenom_ar=request.form['prenom_ar'],
                genre_id=int(request.form['genre_id']),
                date_naissance=datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date(),
                lieu_naissance=request.form['lieu_naissance'],
                date_deces=datetime.strptime(request.form['date_deces'], '%Y-%m-%d').date() if request.form.get('date_deces') else None,
                lieu_deces=request.form.get('lieu_deces')
            )

            db.session.add(enfant)
            db.session.commit()

            flash('Enfant ajouté avec succès!', 'success')
            return redirect(url_for('rh.gestion_enfants', matricule=matricule))

        genres = ReferentielGenre.query.all()
        return render_template('rh/nouveau_enfant.html', militaire=militaire, genres=genres)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout de l\'enfant: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_enfants', matricule=matricule))

@rh_bp.route('/enfant/<int:enfant_id>/modifier', methods=['GET', 'POST'])
def modifier_enfant(enfant_id):
    """Modification d'un enfant"""
    try:
        enfant = Enfant.query.get_or_404(enfant_id)

        if request.method == 'POST':
            enfant.nom = request.form['nom']
            enfant.prenom = request.form['prenom']
            enfant.nom_ar = request.form['nom_ar']
            enfant.prenom_ar = request.form['prenom_ar']
            enfant.genre_id = int(request.form['genre_id'])
            enfant.date_naissance = datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date()
            enfant.lieu_naissance = request.form['lieu_naissance']
            enfant.date_deces = datetime.strptime(request.form['date_deces'], '%Y-%m-%d').date() if request.form.get('date_deces') else None
            enfant.lieu_deces = request.form.get('lieu_deces')

            db.session.commit()
            flash('Enfant modifié avec succès!', 'success')
            return redirect(url_for('rh.gestion_enfants', matricule=enfant.matricule))

        genres = ReferentielGenre.query.all()
        return render_template('rh/modifier_enfant.html', enfant=enfant, genres=genres)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la modification: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_enfants', matricule=enfant.matricule))

@rh_bp.route('/enfant/<int:enfant_id>/supprimer', methods=['POST'])
def supprimer_enfant(enfant_id):
    """Suppression d'un enfant"""
    try:
        enfant = Enfant.query.get_or_404(enfant_id)
        matricule = enfant.matricule

        db.session.delete(enfant)
        db.session.commit()

        flash('Enfant supprimé avec succès!', 'success')
        return redirect(url_for('rh.gestion_enfants', matricule=matricule))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la suppression: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_enfants', matricule=enfant.matricule))

# =============================================================================
# SECTION MÉDICALE
# =============================================================================

@rh_bp.route('/personnel/<matricule>/medical')
def section_medicale(matricule):
    """Section médicale complète d'un militaire"""
    try:
        militaire = Personnel.query.get_or_404(matricule)
        situation_medicale = SituationMedicale.query.filter_by(matricule=matricule).first()
        hospitalisations = Hospitalisation.query.filter_by(matricule=matricule).order_by(Hospitalisation.date_entree.desc()).all()
        vaccinations = Vaccination.query.filter_by(matricule=matricule).order_by(Vaccination.date_vaccination.desc()).all()
        ptcs = PTC.query.filter_by(matricule=matricule).order_by(PTC.date_debut.desc()).all()

        return render_template('rh/section_medicale.html',
                             militaire=militaire,
                             situation_medicale=situation_medicale,
                             hospitalisations=hospitalisations,
                             vaccinations=vaccinations,
                             ptcs=ptcs)

    except Exception as e:
        flash(f'Erreur lors du chargement de la section médicale: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/medical/situation', methods=['GET', 'POST'])
def gestion_situation_medicale(matricule):
    """Gestion de la situation médicale générale"""
    try:
        militaire = Personnel.query.get_or_404(matricule)
        situation = SituationMedicale.query.filter_by(matricule=matricule).first()

        if request.method == 'POST':
            if situation:
                situation.aptitude = request.form['aptitude']
                situation.date_derniere_visite = datetime.strptime(request.form['date_derniere_visite'], '%Y-%m-%d').date() if request.form.get('date_derniere_visite') else None
                situation.observations_generales = request.form.get('observations_generales')
                flash('Situation médicale mise à jour!', 'success')
            else:
                situation = SituationMedicale(
                    matricule=matricule,
                    aptitude=request.form['aptitude'],
                    date_derniere_visite=datetime.strptime(request.form['date_derniere_visite'], '%Y-%m-%d').date() if request.form.get('date_derniere_visite') else None,
                    observations_generales=request.form.get('observations_generales')
                )
                db.session.add(situation)
                flash('Situation médicale créée!', 'success')

            db.session.commit()
            return redirect(url_for('rh.section_medicale', matricule=matricule))

        return render_template('rh/gestion_situation_medicale.html', militaire=militaire, situation=situation)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la gestion de la situation médicale: {str(e)}', 'error')
        return redirect(url_for('rh.section_medicale', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/medical/hospitalisation/nouvelle', methods=['GET', 'POST'])
def nouvelle_hospitalisation(matricule):
    """Ajout d'une nouvelle hospitalisation"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            hospitalisation = Hospitalisation(
                matricule=matricule,
                date_entree=datetime.strptime(request.form['date_entree'], '%Y-%m-%d').date(),
                date_sortie=datetime.strptime(request.form['date_sortie'], '%Y-%m-%d').date() if request.form.get('date_sortie') else None,
                etablissement=request.form['etablissement'],
                motif=request.form['motif'],
                diagnostic=request.form.get('diagnostic'),
                traitement=request.form.get('traitement'),
                observations=request.form.get('observations')
            )

            db.session.add(hospitalisation)
            db.session.commit()

            flash('Hospitalisation ajoutée avec succès!', 'success')
            return redirect(url_for('rh.section_medicale', matricule=matricule))

        return render_template('rh/nouvelle_hospitalisation.html', militaire=militaire)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout de l\'hospitalisation: {str(e)}', 'error')
        return redirect(url_for('rh.section_medicale', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/medical/vaccination/nouvelle', methods=['GET', 'POST'])
def nouvelle_vaccination(matricule):
    """Ajout d'une nouvelle vaccination"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            vaccination = Vaccination(
                matricule=matricule,
                nom_vaccin=request.form['nom_vaccin'],
                date_vaccination=datetime.strptime(request.form['date_vaccination'], '%Y-%m-%d').date(),
                numero_lot=request.form.get('numero_lot'),
                lieu_vaccination=request.form.get('lieu_vaccination'),
                medecin_vaccinateur=request.form.get('medecin_vaccinateur'),
                rappel_prevu=datetime.strptime(request.form['rappel_prevu'], '%Y-%m-%d').date() if request.form.get('rappel_prevu') else None,
                observations=request.form.get('observations')
            )

            db.session.add(vaccination)
            db.session.commit()

            flash('Vaccination ajoutée avec succès!', 'success')
            return redirect(url_for('rh.section_medicale', matricule=matricule))

        return render_template('rh/nouvelle_vaccination.html', militaire=militaire)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout de la vaccination: {str(e)}', 'error')
        return redirect(url_for('rh.section_medicale', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/medical/ptc/nouveau', methods=['GET', 'POST'])
def nouveau_ptc(matricule):
    """Ajout d'un nouveau PTC"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            date_debut = datetime.strptime(request.form['date_debut'], '%Y-%m-%d').date()
            date_fin = datetime.strptime(request.form['date_fin'], '%Y-%m-%d').date()
            duree_jours = (date_fin - date_debut).days + 1

            ptc = PTC(
                matricule=matricule,
                date_debut=date_debut,
                date_fin=date_fin,
                duree_jours=duree_jours,
                objet=request.form['objet'],
                lieu_ptc=request.form.get('lieu_ptc'),
                numero_decision=request.form.get('numero_decision'),
                observations=request.form.get('observations')
            )

            db.session.add(ptc)
            db.session.commit()

            flash('PTC ajouté avec succès!', 'success')
            return redirect(url_for('rh.section_medicale', matricule=matricule))

        return render_template('rh/nouveau_ptc.html', militaire=militaire)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout du PTC: {str(e)}', 'error')
        return redirect(url_for('rh.section_medicale', matricule=matricule))

# =============================================================================
# GESTION DES ABSENCES
# =============================================================================

@rh_bp.route('/personnel/<matricule>/absences')
def gestion_absences(matricule):
    """Gestion complète des absences d'un militaire"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        # Récupération de toutes les absences
        permissions = Permission.query.filter_by(matricule=matricule).order_by(Permission.date_debut.desc()).all()
        desertions = Desertion.query.filter_by(matricule=matricule).order_by(Desertion.date_absence.desc()).all()
        detachements = Detachement.query.filter_by(matricule=matricule).order_by(Detachement.date_debut.desc()).all()

        return render_template('rh/gestion_absences.html',
                             militaire=militaire,
                             permissions=permissions,
                             desertions=desertions,
                             detachements=detachements)

    except Exception as e:
        flash(f'Erreur lors du chargement des absences: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/absence/permission/nouvelle', methods=['GET', 'POST'])
def nouvelle_permission(matricule):
    """Ajout d'une nouvelle permission"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            date_debut = datetime.strptime(request.form['date_debut'], '%Y-%m-%d').date()
            date_fin = datetime.strptime(request.form['date_fin'], '%Y-%m-%d').date()
            duree_jours = (date_fin - date_debut).days + 1

            permission = Permission(
                matricule=matricule,
                type_absence_id=int(request.form['type_absence_id']),
                date_debut=date_debut,
                date_fin=date_fin,
                duree_jours=duree_jours,
                adresse_permission=request.form.get('adresse_permission'),
                numero_serie=request.form.get('numero_serie'),
                motif=request.form.get('motif'),
                observations=request.form.get('observations')
            )

            db.session.add(permission)
            db.session.commit()

            flash('Permission ajoutée avec succès!', 'success')
            return redirect(url_for('rh.gestion_absences', matricule=matricule))

        types_absences = ReferentielTypeAbsence.query.all()
        return render_template('rh/nouvelle_permission.html', militaire=militaire, types_absences=types_absences)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout de la permission: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_absences', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/absence/desertion/nouvelle', methods=['GET', 'POST'])
def nouvelle_desertion(matricule):
    """Ajout d'une nouvelle désertion"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            desertion = Desertion(
                matricule=matricule,
                date_absence=datetime.strptime(request.form['date_absence'], '%Y-%m-%d').date(),
                date_retour=datetime.strptime(request.form['date_retour'], '%Y-%m-%d').date() if request.form.get('date_retour') else None,
                date_arret_solde=datetime.strptime(request.form['date_arret_solde'], '%Y-%m-%d').date() if request.form.get('date_arret_solde') else None,
                date_prise_solde=datetime.strptime(request.form['date_prise_solde'], '%Y-%m-%d').date() if request.form.get('date_prise_solde') else None,
                motif=request.form.get('motif'),
                circonstances=request.form.get('circonstances'),
                sanctions=request.form.get('sanctions'),
                observations=request.form.get('observations')
            )

            db.session.add(desertion)
            db.session.commit()

            flash('Désertion enregistrée avec succès!', 'success')
            return redirect(url_for('rh.gestion_absences', matricule=matricule))

        return render_template('rh/nouvelle_desertion.html', militaire=militaire)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'enregistrement de la désertion: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_absences', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/absence/detachement/nouveau', methods=['GET', 'POST'])
def nouveau_detachement(matricule):
    """Ajout d'un nouveau détachement"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            detachement = Detachement(
                matricule=matricule,
                date_debut=datetime.strptime(request.form['date_debut'], '%Y-%m-%d').date(),
                date_fin=datetime.strptime(request.form['date_fin'], '%Y-%m-%d').date() if request.form.get('date_fin') else None,
                lieu_detachement=request.form['lieu_detachement'],
                adresse_detachement=request.form.get('adresse_detachement'),
                pays=request.form.get('pays', 'Maroc'),
                organisme_accueil=request.form.get('organisme_accueil'),
                fonction_detachement=request.form.get('fonction_detachement'),
                numero_decision=request.form.get('numero_decision'),
                observations=request.form.get('observations')
            )

            db.session.add(detachement)
            db.session.commit()

            flash('Détachement ajouté avec succès!', 'success')
            return redirect(url_for('rh.gestion_absences', matricule=matricule))

        return render_template('rh/nouveau_detachement.html', militaire=militaire)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout du détachement: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_absences', matricule=matricule))

# =============================================================================
# GESTION DES MOUVEMENTS
# =============================================================================

@rh_bp.route('/personnel/<matricule>/mouvements')
def gestion_mouvements(matricule):
    """Gestion complète des mouvements d'un militaire"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        # Récupération de tous les mouvements
        mutations = MutationInterBie.query.filter_by(matricule=matricule).order_by(MutationInterBie.date_mutation.desc()).all()
        sejours_ops = SejourOperationnel.query.filter_by(matricule=matricule).order_by(SejourOperationnel.date_debut.desc()).all()
        liberations = Liberation.query.filter_by(matricule=matricule).order_by(Liberation.date_liberation.desc()).all()
        sanctions = Sanction.query.filter_by(matricule=matricule).order_by(Sanction.date_sanction.desc()).all()

        return render_template('rh/gestion_mouvements.html',
                             militaire=militaire,
                             mutations=mutations,
                             sejours_ops=sejours_ops,
                             liberations=liberations,
                             sanctions=sanctions)

    except Exception as e:
        flash(f'Erreur lors du chargement des mouvements: {str(e)}', 'error')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/mouvement/mutation/nouvelle', methods=['GET', 'POST'])
def nouvelle_mutation(matricule):
    """Ajout d'une nouvelle mutation inter-bie"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            mutation = MutationInterBie(
                matricule=matricule,
                unite_origine_id=int(request.form['unite_origine_id']),
                unite_destination_id=int(request.form['unite_destination_id']),
                fonction_origine=request.form['fonction_origine'],
                fonction_destination=request.form['fonction_destination'],
                date_mutation=datetime.strptime(request.form['date_mutation'], '%Y-%m-%d').date(),
                numero_decision=request.form.get('numero_decision'),
                motif=request.form.get('motif'),
                observations=request.form.get('observations')
            )

            db.session.add(mutation)
            db.session.commit()

            flash('Mutation ajoutée avec succès!', 'success')
            return redirect(url_for('rh.gestion_mouvements', matricule=matricule))

        unites = ReferentielUnite.query.order_by(ReferentielUnite.libelle).all()
        return render_template('rh/nouvelle_mutation.html', militaire=militaire, unites=unites)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout de la mutation: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_mouvements', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/mouvement/sejour_ops/nouveau', methods=['GET', 'POST'])
def nouveau_sejour_ops(matricule):
    """Ajout d'un nouveau séjour opérationnel"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            sejour = SejourOperationnel(
                matricule=matricule,
                unite_operationnelle=request.form['unite_operationnelle'],
                date_debut=datetime.strptime(request.form['date_debut'], '%Y-%m-%d').date(),
                date_fin=datetime.strptime(request.form['date_fin'], '%Y-%m-%d').date() if request.form.get('date_fin') else None,
                lieu_operation=request.form['lieu_operation'],
                type_operation=request.form.get('type_operation'),
                fonction_operationnelle=request.form.get('fonction_operationnelle'),
                numero_decision=request.form.get('numero_decision'),
                observations=request.form.get('observations')
            )

            db.session.add(sejour)
            db.session.commit()

            flash('Séjour opérationnel ajouté avec succès!', 'success')
            return redirect(url_for('rh.gestion_mouvements', matricule=matricule))

        return render_template('rh/nouveau_sejour_ops.html', militaire=militaire)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout du séjour opérationnel: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_mouvements', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/mouvement/liberation/nouvelle', methods=['GET', 'POST'])
def nouvelle_liberation(matricule):
    """Ajout d'une nouvelle libération"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            liberation = Liberation(
                matricule=matricule,
                date_liberation=datetime.strptime(request.form['date_liberation'], '%Y-%m-%d').date(),
                motif_liberation=request.form['motif_liberation'],
                numero_decision=request.form.get('numero_decision'),
                observations=request.form.get('observations')
            )

            db.session.add(liberation)
            db.session.commit()

            flash('Libération ajoutée avec succès!', 'success')
            return redirect(url_for('rh.gestion_mouvements', matricule=matricule))

        return render_template('rh/nouvelle_liberation.html', militaire=militaire)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout de la libération: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_mouvements', matricule=matricule))

@rh_bp.route('/personnel/<matricule>/mouvement/sanction/nouvelle', methods=['GET', 'POST'])
def nouvelle_sanction(matricule):
    """Ajout d'une nouvelle sanction"""
    try:
        militaire = Personnel.query.get_or_404(matricule)

        if request.method == 'POST':
            sanction = Sanction(
                matricule=matricule,
                type_sanction=request.form['type_sanction'],
                date_sanction=datetime.strptime(request.form['date_sanction'], '%Y-%m-%d').date(),
                duree_jours=int(request.form['duree_jours']) if request.form.get('duree_jours') else None,
                motif=request.form['motif'],
                autorite_sanctionnante=request.form.get('autorite_sanctionnante'),
                numero_decision=request.form.get('numero_decision'),
                date_execution=datetime.strptime(request.form['date_execution'], '%Y-%m-%d').date() if request.form.get('date_execution') else None,
                date_fin_execution=datetime.strptime(request.form['date_fin_execution'], '%Y-%m-%d').date() if request.form.get('date_fin_execution') else None,
                observations=request.form.get('observations')
            )

            db.session.add(sanction)
            db.session.commit()

            flash('Sanction ajoutée avec succès!', 'success')
            return redirect(url_for('rh.gestion_mouvements', matricule=matricule))

        return render_template('rh/nouvelle_sanction.html', militaire=militaire)

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout de la sanction: {str(e)}', 'error')
        return redirect(url_for('rh.gestion_mouvements', matricule=matricule))
