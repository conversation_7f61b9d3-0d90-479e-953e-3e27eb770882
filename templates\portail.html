{% extends "base.html" %}

{% block title %}Portail des Applications{% endblock %}

{% block extra_css %}
<style>
    .app-portal-container {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: flex-start; /* Move content higher */
        align-items: center;
        background: var(--background-color);
        position: relative;
        padding-top: 7vh; /* Add top padding for better vertical alignment */
    }
    .app-portal-title {
        font-size: 2.7rem;
        font-weight: 900;
        margin-bottom: 2.2rem; /* slightly reduced */
        color: var(--primary-color);
        letter-spacing: 2px;
        text-shadow: 2px 2px 0 var(--camo-dark), 0 2px 8px #0002;
        text-transform: uppercase;
        font-family: 'Black Ops One', 'Poppins', sans-serif;
    }
    .app-portal-btns {
        display: flex;
        flex-wrap: wrap;
        gap: 2.2rem;
        justify-content: center;
    }
    .app-portal-btn {
        min-width: 240px;
        min-height: 90px;
        font-size: 1.25rem;
        font-weight: 700;
        border-radius: 18px;
        box-shadow: 0 6px 24px rgba(44,62,80,0.13);
        background: linear-gradient(135deg, var(--camo-mid) 60%, var(--camo-tan) 100%);
        color: #fff;
        border: 3px solid var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 1.2rem;
        padding: 0 2.2rem;
        letter-spacing: 1px;
        position: relative;
        overflow: hidden;
        transition: transform 0.18s, box-shadow 0.18s, border-color 0.18s, background 0.18s;
        text-shadow: 1px 1px 2px #0005;
        text-decoration: none;
        outline: none;
    }
    .app-portal-btn svg {
        width: 2.2em;
        height: 2.2em;
        flex-shrink: 0;
        margin-right: 0.7em;
        filter: drop-shadow(0 2px 2px #0003);
    }
    .app-portal-btn:hover, .app-portal-btn:focus {
        transform: translateY(-8px) scale(1.045);
        box-shadow: 0 12px 36px rgba(44,62,80,0.22);
        background: linear-gradient(135deg, var(--camo-dark) 60%, var(--camo-brown) 100%);
        border-color: var(--accent-color);
        color: #ffe;
        text-decoration: none;
    }
    .app-portal-btn:active {
        transform: scale(0.98);
    }
    .app-portal-btn .badge {
        background: var(--accent-color);
        color: #fff;
        font-size: 0.95em;
        font-weight: 700;
        border-radius: 8px;
        padding: 0.2em 0.7em;
        margin-left: auto;
        box-shadow: 0 2px 6px #0002;
    }
    @media (max-width: 600px) {
        .app-portal-btns { flex-direction: column; gap: 1.2rem; }
        .app-portal-btn { min-width: 90vw; padding: 0 1.2rem; }
    }
</style>
{% endblock %}

{% block content %}
<div class="app-portal-container">
    <div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 0.5rem;">
        <span style="font-size:2.1rem; font-weight:900; color:var(--primary-color); letter-spacing:1px; text-shadow:2px 2px 0 var(--camo-dark), 0 2px 8px #0002; font-family:'Black Ops One','Poppins',sans-serif; display:flex; align-items:center; gap:0.7rem;">
            <!-- Activities Icon: Calendar with checkmark -->
            <svg viewBox="0 0 40 40" width="38" height="38" fill="none" style="vertical-align:middle;">
                <rect x="7" y="10" width="26" height="20" rx="5" fill="#A89976" stroke="#2F3A17" stroke-width="2.2"/>
                <rect x="7" y="15" width="26" height="15" rx="4" fill="#6B8E23" stroke="#2F3A17" stroke-width="2.2"/>
                <rect x="12" y="6" width="3" height="8" rx="1.2" fill="#2F3A17"/>
                <rect x="25" y="6" width="3" height="8" rx="1.2" fill="#2F3A17"/>
                <polyline points="15,25 19,29 27,20" fill="none" stroke="#fff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            Gestion des Activités
        </span>
    </div>
    <div class="app-portal-title">
        Portail des Applications
    </div>
    <div class="app-portal-btns">
        <a href="{{ url_for('stages.index') }}" class="app-portal-btn">
            <!-- Book Icon for Stages -->
            <svg viewBox="0 0 48 48" fill="none"><rect x="8" y="10" width="32" height="28" rx="6" fill="#A89976" stroke="#4B5320" stroke-width="2"/><path d="M16 10v28" stroke="#6B8E23" stroke-width="2"/><path d="M32 10v28" stroke="#6B8E23" stroke-width="2"/><path d="M8 18h32" stroke="#4B5320" stroke-width="2"/></svg>
            Gestion des Stages
        </a>
        <a href="#" class="app-portal-btn">
            <!-- Clipboard/Checklist Icon for MCPO -->
            <svg viewBox="0 0 48 48" fill="none"><rect x="12" y="8" width="24" height="32" rx="6" fill="#A89976" stroke="#4B5320" stroke-width="2"/><rect x="18" y="4" width="12" height="8" rx="3" fill="#6B8E23" stroke="#2F3A17" stroke-width="2"/><path d="M18 20h12M18 26h8" stroke="#4B5320" stroke-width="2" stroke-linecap="round"/><circle cx="16" cy="20" r="1.5" fill="#6B8E23"/><circle cx="16" cy="26" r="1.5" fill="#6B8E23"/></svg>
            Gestion MCPO
        </a>
        <a href="{{ url_for('rh.dashboard_rh') }}" class="app-portal-btn">
            <!-- Person Icon for RH -->
            <svg viewBox="0 0 48 48" fill="none"><circle cx="24" cy="18" r="8" fill="#A89976" stroke="#4B5320" stroke-width="2"/><ellipse cx="24" cy="34" rx="14" ry="8" fill="#6B8E23" stroke="#2F3A17" stroke-width="2"/></svg>
            Gestion RH
        </a>
        <a href="#" class="app-portal-btn">
            <!-- Envelope Icon for Courrier -->
            <svg viewBox="0 0 48 48" fill="none"><rect x="6" y="12" width="36" height="24" rx="5" fill="#A89976" stroke="#4B5320" stroke-width="2"/><polyline points="6,12 24,30 42,12" fill="none" stroke="#6B8E23" stroke-width="2.5"/><line x1="6" y1="36" x2="24" y2="22" stroke="#4B5320" stroke-width="2"/><line x1="42" y1="36" x2="24" y2="22" stroke="#4B5320" stroke-width="2"/></svg>
            Gestion du Courrier
        </a>
        <a href="#" class="app-portal-btn">
            <!-- Computer Icon for Informatique -->
            <svg viewBox="0 0 48 48" fill="none"><rect x="8" y="12" width="32" height="20" rx="4" fill="#A89976" stroke="#4B5320" stroke-width="2"/><rect x="16" y="36" width="16" height="4" rx="1.5" fill="#6B8E23" stroke="#2F3A17" stroke-width="2"/><rect x="20" y="32" width="8" height="2" rx="1" fill="#2F3A17"/></svg>
            Gestion de l'Informatique
        </a>
        <a href="#" class="app-portal-btn">
            <!-- Calendar/Plan Icon for Planification -->
            <svg viewBox="0 0 48 48" fill="none"><rect x="8" y="10" width="32" height="28" rx="6" fill="#A89976" stroke="#4B5320" stroke-width="2"/><rect x="8" y="18" width="32" height="20" rx="5" fill="#6B8E23" stroke="#2F3A17" stroke-width="2"/><rect x="14" y="6" width="4" height="8" rx="1.2" fill="#2F3A17"/><rect x="30" y="6" width="4" height="8" rx="1.2" fill="#2F3A17"/><rect x="16" y="22" width="4" height="4" rx="1" fill="#A89976"/><rect x="24" y="22" width="4" height="4" rx="1" fill="#A89976"/><rect x="32" y="22" width="4" height="4" rx="1" fill="#A89976"/></svg>
            Gestion Planification
        </a>
        <a href="#" class="app-portal-btn">
            <!-- Missile/Rocket Icon for ASA -->
            <svg viewBox="0 0 48 48" fill="none"><rect x="20" y="8" width="8" height="24" rx="4" fill="#A89976" stroke="#4B5320" stroke-width="2"/><polygon points="24,4 28,12 20,12" fill="#6B8E23" stroke="#2F3A17" stroke-width="2"/><rect x="22" y="32" width="4" height="8" rx="2" fill="#8B4513"/><polygon points="24,44 28,40 20,40" fill="#4B5320"/></svg>
            Gestion ASA
        </a>
        <a href="/login" class="app-portal-btn">
            <!-- Modern Bold Gear Icon for Technique -->
            <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="24" cy="24" r="10" fill="#A89976" stroke="#2F3A17" stroke-width="3"/>
                <circle cx="24" cy="24" r="5.5" fill="#6B8E23" stroke="#2F3A17" stroke-width="2.5"/>
                <!-- Bold gear teeth -->
                <g stroke="#2F3A17" stroke-width="3" stroke-linecap="round">
                    <line x1="24" y1="4" x2="24" y2="12"/>
                    <line x1="24" y1="36" x2="24" y2="44"/>
                    <line x1="4" y1="24" x2="12" y2="24"/>
                    <line x1="36" y1="24" x2="44" y2="24"/>
                    <line x1="10.5" y1="10.5" x2="16.5" y2="16.5"/>
                    <line x1="31.5" y1="31.5" x2="37.5" y2="37.5"/>
                    <line x1="10.5" y1="37.5" x2="16.5" y2="31.5"/>
                    <line x1="31.5" y1="16.5" x2="37.5" y2="10.5"/>
                </g>
            </svg>
            Gestion Technique
        </a>
    </div>
</div>
{% endblock %}
