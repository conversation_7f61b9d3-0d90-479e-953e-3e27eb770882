"""
Script de génération de données de test finales
200 militaires : 50 Officiers, 50 ODR, 100 MDR
Avec spécifications exactes : CIN (Lettre+6chiffres), Matricule (8 chiffres), noms FR=AR
"""

import random
from datetime import datetime, date, timedelta
from app import app
from db import db
from gestion_vehicules.rh.models import *

# Données réalistes avec correspondance FR-AR
MILITAIRES_DATA = [
    # Format: (nom_fr, prenom_fr, nom_ar, prenom_ar, genre)
    ('ALAMI', 'MOHAMMED', 'العلمي', 'محمد', 'M'),
    ('BENALI', 'AHMED', 'بن علي', 'أحمد', 'M'),
    ('CHAKIR', 'HASSAN', 'شاكر', 'حسن', 'M'),
    ('DRISSI', 'OMAR', 'الدريسي', 'عمر', 'M'),
    ('EL FASSI', 'YOUSSEF', 'الفاسي', 'يوسف', 'M'),
    ('FILALI', 'KHALID', 'الفيلالي', 'خالد', 'M'),
    ('GHAZI', 'ABDELKADER', 'الغازي', 'عبد القادر', 'M'),
    ('HAJJI', 'MUSTAPHA', 'الحاجي', 'مصطفى', 'M'),
    ('IDRISSI', 'SAID', 'الإدريسي', 'سعيد', 'M'),
    ('JAMAL', 'RACHID', 'جمال', 'رشيد', 'M'),
    ('KADIRI', 'ABDELLAH', 'القادري', 'عبد الله', 'M'),
    ('LAHLOU', 'KARIM', 'اللحلو', 'كريم', 'M'),
    ('MANSOURI', 'NOUREDDINE', 'المنصوري', 'نور الدين', 'M'),
    ('NACIRI', 'ABDERRAHIM', 'الناصري', 'عبد الرحيم', 'M'),
    ('OUALI', 'DRISS', 'الوالي', 'إدريس', 'M'),
    ('QADIRI', 'FOUAD', 'القادري', 'فؤاد', 'M'),
    ('RACHIDI', 'JAMAL', 'الرشيدي', 'جمال', 'M'),
    ('SLIMANI', 'LARBI', 'السليماني', 'العربي', 'M'),
    ('TAZI', 'MBAREK', 'التازي', 'مبارك', 'M'),
    ('WAHBI', 'NABIL', 'الوهبي', 'نبيل', 'M'),
    ('ZAKI', 'OTHMANE', 'زكي', 'عثمان', 'M'),
    ('AMRANI', 'REDOUANE', 'العمراني', 'رضوان', 'M'),
    ('BERRADA', 'TARIK', 'برادة', 'طارق', 'M'),
    ('CHRAIBI', 'ZAKARIA', 'الشرايبي', 'زكريا', 'M'),
    ('DOUIRI', 'AMINE', 'الدويري', 'أمين', 'M'),
    ('EL ALAOUI', 'BRAHIM', 'العلوي', 'إبراهيم', 'M'),
    ('FASSI', 'HAMID', 'الفاسي', 'حميد', 'M'),
    ('GUERRAOUI', 'ISMAIL', 'الكراوي', 'إسماعيل', 'M'),
    ('HASSANI', 'MEHDI', 'الحسني', 'مهدي', 'M'),
    ('ISMAILI', 'SAMIR', 'الإسماعيلي', 'سمير', 'M'),
    # Femmes
    ('ALAMI', 'FATIMA', 'العلمي', 'فاطمة', 'F'),
    ('BENALI', 'AICHA', 'بن علي', 'عائشة', 'F'),
    ('CHAKIR', 'KHADIJA', 'شاكر', 'خديجة', 'F'),
    ('DRISSI', 'ZINEB', 'الدريسي', 'زينب', 'F'),
    ('EL FASSI', 'AMINA', 'الفاسي', 'أمينة', 'F'),
    ('FILALI', 'LATIFA', 'الفيلالي', 'لطيفة', 'F'),
    ('GHAZI', 'MALIKA', 'الغازي', 'مليكة', 'F'),
    ('HAJJI', 'NAIMA', 'الحاجي', 'نعيمة', 'F'),
    ('IDRISSI', 'RACHIDA', 'الإدريسي', 'رشيدة', 'F'),
    ('JAMAL', 'SAIDA', 'جمال', 'سعيدة', 'F'),
    ('KADIRI', 'HAFIDA', 'القادري', 'حفيدة', 'F'),
    ('LAHLOU', 'JAMILA', 'اللحلو', 'جميلة', 'F'),
    ('MANSOURI', 'KARIMA', 'المنصوري', 'كريمة', 'F'),
    ('NACIRI', 'LEILA', 'الناصري', 'ليلى', 'F'),
    ('OUALI', 'MERYEM', 'الوالي', 'مريم', 'F'),
    ('QADIRI', 'NADIA', 'القادري', 'نادية', 'F'),
    ('RACHIDI', 'OUAFAE', 'الرشيدي', 'وفاء', 'F'),
    ('SLIMANI', 'RAJAE', 'السليماني', 'رجاء', 'F'),
    ('TAZI', 'SAMIRA', 'التازي', 'سميرة', 'F'),
    ('WAHBI', 'ZAHRA', 'الوهبي', 'زهرة', 'F')
]

VILLES_MAROC = [
    'Rabat', 'Casablanca', 'Fès', 'Marrakech', 'Agadir', 'Tanger', 'Meknès', 'Oujda', 
    'Kenitra', 'Tétouan', 'Safi', 'Mohammedia', 'Khouribga', 'Beni Mellal', 'El Jadida', 
    'Nador', 'Taza', 'Settat', 'Berrechid', 'Khemisset'
]

# Fonctions par catégorie selon les spécifications
FONCTIONS_OFFICIERS = [
    'Commandant d\'Unité', 'Chef d\'État-Major', 'Officier Opérations', 'Officier Renseignement', 
    'Officier Logistique', 'Commandant de Compagnie', 'Chef de Section', 'Officier Adjoint',
    'Responsable Formation', 'Officier Liaison', 'Chef Bureau Personnel', 'Officier Sécurité',
    'Commandant de Batterie', 'Officier Tir', 'Officier Matériel'
]

FONCTIONS_ODR = [
    'Adjudant-Chef d\'Unité', 'Sergent-Chef de Section', 'Instructeur', 'Chef d\'Équipe',
    'Responsable Matériel', 'Sergent Opérations', 'Chef Magasin', 'Formateur Spécialisé',
    'Adjudant Logistique', 'Sergent-Chef Transmissions', 'Chef Atelier', 'Sergent Sécurité',
    'Adjudant Tir', 'Sergent Maintenance'
]

FONCTIONS_MDR = [
    'Soldat Spécialisé', 'Caporal d\'Équipe', 'Opérateur Radio', 'Conducteur', 'Mécanicien',
    'Tireur', 'Servant d\'Arme', 'Garde', 'Magasinier', 'Cuisinier', 'Infirmier', 'Secrétaire',
    'Pointeur', 'Chargeur', 'Servant de Pièce'
]

# Grades selon spécifications (fidèles aux suggestions)
GRADES_OFFICIERS = ['GEN', 'GBR', 'COL', 'LCL', 'CDT', 'CPT', 'LTN', 'SLT', 'ASP']
GRADES_ODR = ['ADC', 'ADJ', 'SGC', 'SGT']
GRADES_MDR = ['CPLC', 'CPL', 'SOL']

def generer_matricule():
    """Génère un matricule de 8 chiffres"""
    return ''.join([str(random.randint(0, 9)) for _ in range(8)])

def generer_cin():
    """Génère un CIN format Lettre + 6 chiffres (ex: D784517)"""
    lettre = random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')
    chiffres = ''.join([str(random.randint(0, 9)) for _ in range(6)])
    return f"{lettre}{chiffres}"

def generer_gsm():
    """Génère un GSM marocain"""
    prefixes = ['06', '07']
    return f"{random.choice(prefixes)}{''.join([str(random.randint(0, 9)) for _ in range(8)])}"

def generer_date_naissance(age_min, age_max):
    """Génère une date de naissance pour un âge donné"""
    age = random.randint(age_min, age_max)
    today = date.today()
    birth_year = today.year - age
    birth_month = random.randint(1, 12)
    birth_day = random.randint(1, 28)
    return date(birth_year, birth_month, birth_day)

def creer_militaires_test():
    """Crée 200 militaires avec toutes les spécifications"""
    try:
        with app.app_context():
            print("🚀 Génération de 200 militaires avec spécifications exactes...")
            
            # Récupération des données de référence
            genres = {g.libelle: g.id_genre for g in ReferentielGenre.query.all()}
            categories = {c.libelle: c.id_categorie for c in ReferentielCategorie.query.all()}
            groupes_sanguins = [g.id_groupe for g in ReferentielGroupeSanguin.query.all()]
            services = [s.id_service for s in ReferentielService.query.all()]
            unites = [u.id_unite for u in ReferentielUnite.query.all()]
            grades_dict = {g.code_grade: g.id_grade for g in ReferentielGrade.query.all()}
            etats_matrimoniaux = [e.id_etat for e in ReferentielEtatMatrimonial.query.all()]
            liens_parente = [l.id_lien for l in ReferentielLienParente.query.all()]
            
            militaires_crees = 0
            matricules_utilises = set()
            
            # Configuration par catégorie
            categories_config = [
                ('Officier', 50, GRADES_OFFICIERS, FONCTIONS_OFFICIERS, 25, 55),
                ('Officier des rangs', 50, GRADES_ODR, FONCTIONS_ODR, 22, 45),
                ('Militaire des rangs', 100, GRADES_MDR, FONCTIONS_MDR, 18, 35)
            ]
            
            for cat_nom, nombre, grades_cat, fonctions_cat, age_min, age_max in categories_config:
                print(f"📊 Génération de {nombre} {cat_nom}s...")
                
                for i in range(nombre):
                    try:
                        # Sélection aléatoire d'un militaire
                        militaire_data = random.choice(MILITAIRES_DATA)
                        nom_fr, prenom_fr, nom_ar, prenom_ar, genre_code = militaire_data
                        
                        # Genre
                        genre_id = genres['Masculin'] if genre_code == 'M' else genres['Féminin']
                        
                        # Matricule unique
                        matricule = generer_matricule()
                        while matricule in matricules_utilises:
                            matricule = generer_matricule()
                        matricules_utilises.add(matricule)
                        
                        # Dates
                        date_naissance = generer_date_naissance(age_min, age_max)
                        annees_service = random.randint(1, min(30, date.today().year - date_naissance.year - 18))
                        date_engagement = date(date.today().year - annees_service, 
                                             random.randint(1, 12), random.randint(1, 28))
                        
                        # CIN avec nouveau format
                        cin_numero = generer_cin()
                        cin_delivrance = date_naissance + timedelta(days=random.randint(6570, 7300))
                        cin_expiration = cin_delivrance + timedelta(days=random.randint(3650, 5475))
                        
                        # Création du militaire
                        militaire = Personnel(
                            matricule=matricule,
                            nom=nom_fr,
                            prenom=prenom_fr,
                            nom_ar=nom_ar,
                            prenom_ar=prenom_ar,
                            date_naissance=date_naissance,
                            lieu_naissance=random.choice(VILLES_MAROC),
                            genre_id=genre_id,
                            categorie_id=categories[cat_nom],
                            groupe_sanguin_id=random.choice(groupes_sanguins),
                            cin_numero=cin_numero,
                            cin_date_delivrance=cin_delivrance,
                            cin_date_expiration=cin_expiration,
                            gsm=generer_gsm(),
                            telephone_domicile=generer_gsm() if random.random() < 0.6 else None,
                            taille_cm=random.randint(155, 190),
                            lieu_residence=f"{random.randint(1, 999)} Rue {random.choice(['Hassan II', 'Mohammed V', 'Al Massira'])}, {random.choice(VILLES_MAROC)}",
                            service_id=random.choice(services),
                            specialite_id=None,
                            unite_id=random.choice(unites),
                            grade_actuel_id=grades_dict[random.choice(grades_cat)],
                            fonction=random.choice(fonctions_cat),
                            date_prise_fonction=date_engagement + timedelta(days=random.randint(0, 365)),
                            date_engagement=date_engagement,
                            ccp_numero=''.join([str(random.randint(0, 9)) for _ in range(12)]),
                            compte_bancaire_numero=''.join([str(random.randint(0, 9)) for _ in range(16)]) if random.random() < 0.7 else None,
                            somme_numero=f"SM{''.join([str(random.randint(0, 9)) for _ in range(8)])}",
                            nom_pere=random.choice([p[1] for p in MILITAIRES_DATA if p[4] == 'M']),
                            prenom_pere=random.choice([p[1] for p in MILITAIRES_DATA if p[4] == 'M']),
                            nom_mere=random.choice([p[1] for p in MILITAIRES_DATA if p[4] == 'F']),
                            prenom_mere=random.choice([p[1] for p in MILITAIRES_DATA if p[4] == 'F']),
                            adresse_parents=f"{random.choice(VILLES_MAROC)}, Maroc",
                            etat_matrimonial_id=random.choice(etats_matrimoniaux),
                            nombre_enfants=random.randint(0, 5) if random.random() < 0.6 else None,
                            passeport_numero=f"{''.join([chr(random.randint(65, 90)) for _ in range(2)])}{''.join([str(random.randint(0, 9)) for _ in range(6)])}" if random.random() < 0.4 else None,
                            passeport_date_delivrance=cin_delivrance + timedelta(days=random.randint(0, 1000)) if random.random() < 0.4 else None,
                            passeport_date_expiration=cin_expiration + timedelta(days=random.randint(0, 1000)) if random.random() < 0.4 else None,
                            gsm_urgence=generer_gsm(),
                            lien_parente_id=random.choice(liens_parente)
                        )
                        
                        db.session.add(militaire)
                        
                        # Situation médicale
                        situation = SituationMedicale(
                            matricule=matricule,
                            aptitude=random.choice(['Apte', 'Apte', 'Apte', 'Apte', 'Inapte']),  # 80% aptes
                            date_derniere_visite=date.today() - timedelta(days=random.randint(30, 365)),
                            observations_generales="Situation normale" if random.random() < 0.8 else "Suivi particulier"
                        )
                        db.session.add(situation)
                        
                        # Historique grade
                        historique = HistoriqueGrade(
                            matricule=matricule,
                            grade_id=militaire.grade_actuel_id,
                            date_debut=date_engagement,
                            numero_decision=f"DEC{random.randint(1000, 9999)}",
                            observations="Grade initial"
                        )
                        db.session.add(historique)
                        
                        militaires_crees += 1
                        
                        # Commit par batch de 25
                        if militaires_crees % 25 == 0:
                            db.session.commit()
                            print(f"✅ {militaires_crees} militaires créés...")
                            
                    except Exception as e:
                        print(f"⚠️ Erreur militaire {militaires_crees + 1}: {str(e)}")
                        db.session.rollback()
                        continue
            
            # Commit final
            db.session.commit()
            print(f"🎉 {militaires_crees} militaires créés avec succès!")
            
            # Statistiques finales
            print("\n📊 STATISTIQUES FINALES:")
            print(f"   • Officiers: {Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == 'Officier').count()}")
            print(f"   • ODR: {Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == 'Officier des rangs').count()}")
            print(f"   • MDR: {Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == 'Militaire des rangs').count()}")
            print(f"   • Total: {Personnel.query.count()}")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur générale: {str(e)}")
        db.session.rollback()
        return False

def main():
    """Fonction principale"""
    print("🚀 GÉNÉRATION DE DONNÉES FINALES RH")
    print("=" * 60)
    print("📋 Spécifications:")
    print("   • 200 militaires (50 OFF, 50 ODR, 100 MDR)")
    print("   • Matricules: 8 chiffres (ex: 12345678)")
    print("   • CIN: Lettre + 6 chiffres (ex: D784517)")
    print("   • Noms FR = Noms AR (correspondance exacte)")
    print("   • Grades fidèles aux spécifications")
    print("   • Situations variées (famille, médical, etc.)")
    print("=" * 60)
    
    if creer_militaires_test():
        print("\n🎉 GÉNÉRATION TERMINÉE AVEC SUCCÈS!")
        print("🔗 Testez l'application:")
        print("   • Dashboard: http://localhost:3000/rh")
        print("   • Recherche: http://localhost:3000/rh/recherche")
    else:
        print("\n❌ ERREUR LORS DE LA GÉNÉRATION")

if __name__ == "__main__":
    main()
