{% extends "rh/base_rh.html" %}

{% block title %}{{ militaire.nom_complet }} - Fiche Personnel{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête de la Fiche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-0">
                                <i class="fas fa-user-shield"></i>
                                {{ militaire.nom_complet }}
                            </h1>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark me-2">{{ militaire.matricule }}</span>
                                <span class="badge bg-info me-2">{{ militaire.unite.code if militaire.unite else 'N/A' }}</span>
                                <span class="badge bg-warning text-dark">{{ militaire.service.code_court if militaire.service else 'N/A' }}</span>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <button class="btn btn-warning" onclick="window.print()">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                                <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alertes Personnalisées -->
    {% if alertes %}
    <div class="row mb-4">
        <div class="col-12">
            {% for alerte in alertes %}
            <div class="alert alert-{{ alerte.type }} alert-dismissible fade show" role="alert">
                <i class="fas fa-{{ alerte.icon }} me-2"></i>
                <strong>Alerte :</strong> {{ alerte.message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Statistiques Rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-birthday-cake stat-icon"></i>
                <div class="stat-number">{{ militaire.age }}</div>
                <div class="stat-label">Ans</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-calendar-alt stat-icon"></i>
                <div class="stat-number">{{ total_permissions }}</div>
                <div class="stat-label">Permissions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-hospital stat-icon"></i>
                <div class="stat-number">{{ total_hospitalisations }}</div>
                <div class="stat-label">Hospitalisations</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-exchange-alt stat-icon"></i>
                <div class="stat-number">{{ total_mutations }}</div>
                <div class="stat-label">Mutations</div>
            </div>
        </div>
    </div>

    <!-- Onglets Principaux -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <ul class="nav nav-tabs card-header-tabs" id="fichePersonnelTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personnel-tab" data-bs-toggle="tab" data-bs-target="#personnel" type="button" role="tab">
                                <i class="fas fa-user"></i> Personnel
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="famille-tab" data-bs-toggle="tab" data-bs-target="#famille" type="button" role="tab">
                                <i class="fas fa-home"></i> Famille
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="medical-tab" data-bs-toggle="tab" data-bs-target="#medical" type="button" role="tab">
                                <i class="fas fa-heartbeat"></i> Médical
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="absences-tab" data-bs-toggle="tab" data-bs-target="#absences" type="button" role="tab">
                                <i class="fas fa-calendar-times"></i> Absences
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="mouvements-tab" data-bs-toggle="tab" data-bs-target="#mouvements" type="button" role="tab">
                                <i class="fas fa-route"></i> Mouvements
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="historique-tab" data-bs-toggle="tab" data-bs-target="#historique" type="button" role="tab">
                                <i class="fas fa-history"></i> Historique
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="fichePersonnelTabsContent">
                        <!-- Onglet Personnel -->
                        <div class="tab-pane fade show active" id="personnel" role="tabpanel">
                            <div class="row">
                                <!-- Informations Personnelles -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-user me-2"></i>Informations Personnelles</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>Nom complet :</strong></td><td>{{ militaire.nom }} {{ militaire.prenom }}</td></tr>
                                        <tr><td><strong>Nom (Arabe) :</strong></td><td dir="rtl">{{ militaire.nom_ar }} {{ militaire.prenom_ar }}</td></tr>
                                        <tr><td><strong>Date de naissance :</strong></td><td>{{ militaire.date_naissance.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Lieu de naissance :</strong></td><td>{{ militaire.lieu_naissance }}</td></tr>
                                        <tr><td><strong>Âge :</strong></td><td>{{ militaire.age }} ans</td></tr>
                                        <tr><td><strong>Genre :</strong></td><td>{{ militaire.genre.libelle if militaire.genre else 'N/A' }}</td></tr>
                                        <tr><td><strong>Groupe sanguin :</strong></td><td><span class="badge bg-danger">{{ militaire.groupe_sanguin.libelle if militaire.groupe_sanguin else 'N/A' }}</span></td></tr>
                                        <tr><td><strong>Taille :</strong></td><td>{{ militaire.taille_cm }} cm</td></tr>
                                    </table>
                                </div>

                                <!-- Informations Militaires -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-shield-alt me-2"></i>Informations Militaires</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>Matricule :</strong></td><td><span class="badge bg-primary">{{ militaire.matricule }}</span></td></tr>
                                        <tr><td><strong>Grade :</strong></td><td><span class="badge bg-success">{{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'N/A' }}</span></td></tr>
                                        <tr><td><strong>Catégorie :</strong></td><td>{{ militaire.categorie.libelle if militaire.categorie else 'N/A' }}</td></tr>
                                        <tr><td><strong>Service/Arme :</strong></td><td>{{ militaire.service.libelle if militaire.service else 'N/A' }}</td></tr>
                                        <tr><td><strong>Spécialité :</strong></td><td>{{ militaire.specialite.libelle if militaire.specialite else 'Aucune' }}</td></tr>
                                        <tr><td><strong>Unité :</strong></td><td>{{ militaire.unite.libelle if militaire.unite else 'N/A' }}</td></tr>
                                        <tr><td><strong>Fonction :</strong></td><td>{{ militaire.fonction }}</td></tr>
                                        <tr><td><strong>Date engagement :</strong></td><td>{{ militaire.date_engagement.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Date prise fonction :</strong></td><td>{{ militaire.date_prise_fonction.strftime('%d/%m/%Y') }}</td></tr>
                                    </table>
                                </div>

                                <!-- Documents et Coordonnées -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-id-card me-2"></i>Documents d'Identité</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>CIN :</strong></td><td>{{ militaire.cin_numero }}</td></tr>
                                        <tr><td><strong>Date délivrance :</strong></td><td>{{ militaire.cin_date_delivrance.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Date expiration :</strong></td><td>
                                            {% set jours_restants = (militaire.cin_date_expiration - date.today()).days %}
                                            {{ militaire.cin_date_expiration.strftime('%d/%m/%Y') }}
                                            {% if jours_restants <= 0 %}
                                            <span class="badge bg-danger ms-2">Expirée</span>
                                            {% elif jours_restants <= 30 %}
                                            <span class="badge bg-warning text-dark ms-2">{{ jours_restants }}j</span>
                                            {% endif %}
                                        </td></tr>
                                        {% if militaire.passeport_numero %}
                                        <tr><td><strong>Passeport :</strong></td><td>{{ militaire.passeport_numero }}</td></tr>
                                        {% endif %}
                                    </table>
                                </div>

                                <!-- Coordonnées -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-phone me-2"></i>Coordonnées</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>GSM :</strong></td><td><a href="tel:{{ militaire.gsm }}">{{ militaire.gsm }}</a></td></tr>
                                        {% if militaire.telephone_domicile %}
                                        <tr><td><strong>Téléphone domicile :</strong></td><td><a href="tel:{{ militaire.telephone_domicile }}">{{ militaire.telephone_domicile }}</a></td></tr>
                                        {% endif %}
                                        <tr><td><strong>Résidence :</strong></td><td>{{ militaire.lieu_residence }}</td></tr>
                                        <tr><td><strong>GSM urgence :</strong></td><td><a href="tel:{{ militaire.gsm_urgence }}">{{ militaire.gsm_urgence }}</a></td></tr>
                                        <tr><td><strong>Lien parenté :</strong></td><td>{{ militaire.lien_parente.libelle if militaire.lien_parente else 'N/A' }}</td></tr>
                                    </table>
                                </div>

                                <!-- Informations Bancaires -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-university me-2"></i>Informations Bancaires</h5>
                                    <table class="table table-sm">
                                        <tr><td><strong>CCP :</strong></td><td>{{ militaire.ccp_numero if militaire.ccp_numero else 'Non renseigné' }}</td></tr>
                                        {% if militaire.compte_bancaire_numero %}
                                        <tr><td><strong>Compte bancaire :</strong></td><td>{{ militaire.compte_bancaire_numero }}</td></tr>
                                        {% else %}
                                        <tr><td><strong>Compte bancaire :</strong></td><td>Non renseigné</td></tr>
                                        {% endif %}
                                        <tr><td><strong>N° SOMME :</strong></td><td>{{ militaire.somme_numero if militaire.somme_numero else 'Non renseigné' }}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Famille -->
                        <div class="tab-pane fade" id="famille" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-heart me-2"></i>Gestion Familiale</h5>
                                <a href="{{ url_for('rh.gestion_famille', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-cog"></i> Gérer la Famille
                                </a>
                            </div>
                            <div class="row">
                                <!-- Informations Parents -->
                                <div class="col-lg-6 mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-users me-2"></i>Parents</h5>
                                    </div>
                                    <table class="table table-sm">
                                        <tr><td><strong>Père :</strong></td><td>{{ militaire.nom_pere }} {{ militaire.prenom_pere }}</td></tr>
                                        <tr><td><strong>Mère :</strong></td><td>{{ militaire.nom_mere }} {{ militaire.prenom_mere }}</td></tr>
                                        <tr><td><strong>Adresse parents :</strong></td><td>{{ militaire.adresse_parents }}</td></tr>
                                        <tr><td><strong>État matrimonial :</strong></td><td>
                                            <span class="badge bg-info">{{ militaire.etat_matrimonial.libelle if militaire.etat_matrimonial else 'N/A' }}</span>
                                        </td></tr>
                                        {% if militaire.nombre_enfants %}
                                        <tr><td><strong>Nombre d'enfants :</strong></td><td>{{ militaire.nombre_enfants }}</td></tr>
                                        {% endif %}
                                    </table>
                                </div>

                                <!-- Conjoint -->
                                <div class="col-lg-6 mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-heart me-2"></i>Conjoint</h5>
                                        <a href="{{ url_for('rh.gestion_famille', matricule=militaire.matricule) }}" class="btn btn-sm btn-success-military">
                                            <i class="fas fa-{{ 'edit' if conjoint else 'plus' }}"></i> {{ 'Modifier' if conjoint else 'Ajouter' }}
                                        </a>
                                    </div>
                                    {% if conjoint %}
                                    <table class="table table-sm">
                                        <tr><td><strong>Nom complet :</strong></td><td>{{ conjoint.nom }} {{ conjoint.prenom }}</td></tr>
                                        <tr><td><strong>Date naissance :</strong></td><td>{{ conjoint.date_naissance.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Lieu naissance :</strong></td><td>{{ conjoint.lieu_naissance }}</td></tr>
                                        <tr><td><strong>CIN :</strong></td><td>{{ conjoint.cin_numero }}</td></tr>
                                        <tr><td><strong>Date mariage :</strong></td><td>{{ conjoint.date_mariage.strftime('%d/%m/%Y') }}</td></tr>
                                        <tr><td><strong>Lieu mariage :</strong></td><td>{{ conjoint.lieu_mariage }}</td></tr>
                                        {% if conjoint.profession %}
                                        <tr><td><strong>Profession :</strong></td><td>{{ conjoint.profession }}</td></tr>
                                        {% endif %}
                                        {% if conjoint.gsm %}
                                        <tr><td><strong>GSM :</strong></td><td><a href="tel:{{ conjoint.gsm }}">{{ conjoint.gsm }}</a></td></tr>
                                        {% endif %}
                                    </table>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune information de conjoint</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Enfants -->
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-child me-2"></i>Enfants ({{ enfants|length }})</h5>
                                        <a href="{{ url_for('rh.gestion_famille', matricule=militaire.matricule) }}" class="btn btn-sm btn-success-military">
                                            <i class="fas fa-cog"></i> Gérer les Enfants
                                        </a>
                                    </div>
                                    {% if enfants %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Nom Complet</th>
                                                    <th>Genre</th>
                                                    <th>Date Naissance</th>
                                                    <th>Âge</th>
                                                    <th>Lieu Naissance</th>
                                                    <th>Statut</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for enfant in enfants %}
                                                <tr>
                                                    <td><strong>{{ enfant.nom }} {{ enfant.prenom }}</strong></td>
                                                    <td><span class="badge bg-{{ 'primary' if enfant.genre.libelle == 'Masculin' else 'pink' }}">{{ enfant.genre.libelle if enfant.genre else 'N/A' }}</span></td>
                                                    <td>{{ enfant.date_naissance.strftime('%d/%m/%Y') }}</td>
                                                    <td>{{ enfant.age }} ans</td>
                                                    <td>{{ enfant.lieu_naissance }}</td>
                                                    <td>
                                                        {% if enfant.est_vivant %}
                                                        <span class="badge bg-success">Vivant</span>
                                                        {% else %}
                                                        <span class="badge bg-secondary">Décédé le {{ enfant.date_deces.strftime('%d/%m/%Y') }}</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-child fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucun enfant enregistré</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Médical -->
                        <div class="tab-pane fade" id="medical" role="tabpanel">
                            <div class="row">
                                <!-- Situation Médicale -->
                                <div class="col-lg-6 mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-heartbeat me-2"></i>Situation Médicale</h5>
                                        <a href="{{ url_for('rh.gestion_medical', matricule=militaire.matricule) }}" class="btn btn-sm btn-success-military">
                                            <i class="fas fa-cog"></i> Gérer
                                        </a>
                                    </div>
                                    {% if situation_medicale %}
                                    <table class="table table-sm">
                                        <tr><td><strong>Aptitude :</strong></td><td>
                                            <span class="badge bg-{{ 'success' if situation_medicale.aptitude == 'Apte' else 'danger' }}">
                                                {{ situation_medicale.aptitude }}
                                            </span>
                                        </td></tr>
                                        {% if situation_medicale.date_derniere_visite %}
                                        <tr><td><strong>Dernière visite :</strong></td><td>{{ situation_medicale.date_derniere_visite.strftime('%d/%m/%Y') }}</td></tr>
                                        {% endif %}
                                        {% if situation_medicale.observations_generales %}
                                        <tr><td><strong>Observations :</strong></td><td>{{ situation_medicale.observations_generales[:100] }}{% if situation_medicale.observations_generales|length > 100 %}...{% endif %}</td></tr>
                                        {% endif %}
                                    </table>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-heartbeat fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune situation médicale</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Hospitalisations Récentes -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-hospital me-2"></i>Hospitalisations Récentes</h5>
                                    {% if hospitalisations %}
                                    <div class="list-group">
                                        {% for hosp in hospitalisations %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ hosp.etablissement }}</h6>
                                                <small>{{ hosp.date_entree.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <p class="mb-1">{{ hosp.motif[:80] }}{% if hosp.motif|length > 80 %}...{% endif %}</p>
                                            <small>Durée: {{ hosp.duree_jours }} jour(s)</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-hospital fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune hospitalisation</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Vaccinations et PTC -->
                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-syringe me-2"></i>Vaccinations Récentes</h5>
                                    {% if vaccinations %}
                                    <div class="list-group">
                                        {% for vacc in vaccinations %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ vacc.nom_vaccin }}</h6>
                                                <small>{{ vacc.date_vaccination.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            {% if vacc.rappel_prevu %}
                                            <small>Rappel prévu: {{ vacc.rappel_prevu.strftime('%d/%m/%Y') }}</small>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-syringe fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune vaccination</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-lg-6 mb-4">
                                    <h5><i class="fas fa-bed me-2"></i>PTC Récents</h5>
                                    {% if ptcs %}
                                    <div class="list-group">
                                        {% for ptc in ptcs %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ ptc.objet[:50] }}{% if ptc.objet|length > 50 %}...{% endif %}</h6>
                                                <small>{{ ptc.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>Durée: {{ ptc.duree_jours }} jour(s)</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-bed fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucun PTC</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Absences -->
                        <div class="tab-pane fade" id="absences" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-calendar-times me-2"></i>Gestion des Absences</h5>
                                <a href="{{ url_for('rh.gestion_permissions', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-cog"></i> Gérer les Absences
                                </a>
                            </div>
                            
                            <div class="row">
                                <!-- Permissions -->
                                <div class="col-lg-4 mb-4">
                                    <h6><i class="fas fa-calendar-check me-2"></i>Permissions ({{ permissions|length }})</h6>
                                    {% if permissions %}
                                    <div class="list-group">
                                        {% for perm in permissions %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ perm.type_absence.libelle if perm.type_absence else 'N/A' }}</h6>
                                                <small>{{ perm.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ perm.duree_jours }} jour(s)</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune permission</p>
                                    {% endif %}
                                </div>

                                <!-- Détachements -->
                                <div class="col-lg-4 mb-4">
                                    <h6><i class="fas fa-plane me-2"></i>Détachements ({{ detachements|length }})</h6>
                                    {% if detachements %}
                                    <div class="list-group">
                                        {% for det in detachements %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ det.lieu_detachement }}</h6>
                                                <small>{{ det.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ det.pays }} - {% if det.en_cours %}En cours{% else %}Terminé{% endif %}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucun détachement</p>
                                    {% endif %}
                                </div>

                                <!-- Désertions -->
                                <div class="col-lg-4 mb-4">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Désertions ({{ desertions|length }})</h6>
                                    {% if desertions %}
                                    <div class="list-group">
                                        {% for des in desertions %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">Désertion</h6>
                                                <small>{{ des.date_absence.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small class="text-{{ 'danger' if des.en_cours else 'success' }}">
                                                {% if des.en_cours %}En cours{% else %}Retour le {{ des.date_retour.strftime('%d/%m/%Y') }}{% endif %}
                                            </small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune désertion</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Mouvements -->
                        <div class="tab-pane fade" id="mouvements" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-route me-2"></i>Mouvements et Sanctions</h5>
                                <a href="{{ url_for('rh.gestion_mutations', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-cog"></i> Gérer les Mouvements
                                </a>
                            </div>
                            
                            <div class="row">
                                <!-- Mutations -->
                                <div class="col-lg-6 mb-4">
                                    <h6><i class="fas fa-exchange-alt me-2"></i>Mutations Inter-Bie ({{ mutations|length }})</h6>
                                    {% if mutations %}
                                    <div class="list-group">
                                        {% for mut in mutations %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ mut.unite_origine.code if mut.unite_origine else 'N/A' }} → {{ mut.unite_destination.code if mut.unite_destination else 'N/A' }}</h6>
                                                <small>{{ mut.date_mutation.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ mut.fonction_destination }}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune mutation</p>
                                    {% endif %}
                                </div>

                                <!-- Séjours Opérationnels -->
                                <div class="col-lg-6 mb-4">
                                    <h6><i class="fas fa-map-marked-alt me-2"></i>Séjours Opérationnels ({{ sejours_ops|length }})</h6>
                                    {% if sejours_ops %}
                                    <div class="list-group">
                                        {% for sej in sejours_ops %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ sej.unite_operationnelle }}</h6>
                                                <small>{{ sej.date_debut.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ sej.lieu_operation }} - {% if sej.en_cours %}En cours{% else %}Terminé{% endif %}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucun séjour opérationnel</p>
                                    {% endif %}
                                </div>

                                <!-- Sanctions -->
                                <div class="col-lg-6 mb-4">
                                    <h6><i class="fas fa-gavel me-2"></i>Sanctions ({{ sanctions|length }})</h6>
                                    {% if sanctions %}
                                    <div class="list-group">
                                        {% for sanc in sanctions %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ sanc.type_sanction }}</h6>
                                                <small>{{ sanc.date_sanction.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                            <small>{{ sanc.motif[:80] }}{% if sanc.motif|length > 80 %}...{% endif %}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune sanction</p>
                                    {% endif %}
                                </div>

                                <!-- Libérations -->
                                <div class="col-lg-6 mb-4">
                                    <h6><i class="fas fa-door-open me-2"></i>Libérations ({{ liberations|length }})</h6>
                                    {% if liberations %}
                                    <div class="list-group">
                                        {% for lib in liberations %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ lib.motif_liberation }}</h6>
                                                <small>{{ lib.date_liberation.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">Aucune libération</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Historique -->
                        <div class="tab-pane fade" id="historique" role="tabpanel">
                            <div class="row">
                                <!-- Historique des Grades -->
                                <div class="col-lg-8 mb-4">
                                    <h5><i class="fas fa-star me-2"></i>Évolution des Grades</h5>
                                    {% if historique_grades %}
                                    <div class="timeline">
                                        {% for hist in historique_grades %}
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary"></div>
                                            <div class="timeline-content">
                                                <h6 class="timeline-title">{{ hist.grade.libelle if hist.grade else 'N/A' }}</h6>
                                                <p class="timeline-text">
                                                    <strong>Date :</strong> {{ hist.date_debut.strftime('%d/%m/%Y') }}
                                                    {% if hist.date_fin %} - {{ hist.date_fin.strftime('%d/%m/%Y') }}{% endif %}
                                                </p>
                                                {% if hist.numero_decision %}
                                                <small class="text-muted">Décision N° {{ hist.numero_decision }}</small>
                                                {% endif %}
                                                {% if hist.observations %}
                                                <p class="mt-2"><small>{{ hist.observations }}</small></p>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucun historique de grade</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Langues Parlées -->
                                <div class="col-lg-4 mb-4">
                                    <h5><i class="fas fa-language me-2"></i>Langues Parlées</h5>
                                    {% if langues %}
                                    <div class="list-group">
                                        {% for lang in langues %}
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            {{ lang.langue.libelle if lang.langue else 'N/A' }}
                                            <span class="badge bg-primary">{{ lang.niveau }}</span>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-language fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune langue renseignée</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.nav-tabs .nav-link {
    color: var(--text-light);
    border: none;
    background: transparent;
    margin-right: 10px;
    border-radius: 25px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background: rgba(212, 175, 55, 0.2);
    color: var(--accent-color);
}

.nav-tabs .nav-link.active {
    background: var(--accent-color);
    color: var(--primary-color);
    font-weight: 600;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--accent-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -37px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 3px solid var(--accent-color);
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid var(--accent-color);
}

.timeline-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 5px;
}

.list-group-item {
    border: 1px solid #dee2e6;
    margin-bottom: 5px;
    border-radius: 8px;
}

.badge {
    font-size: 0.75em;
}

@media print {
    .btn, .nav-tabs {
        display: none !important;
    }
    
    .tab-content .tab-pane {
        display: block !important;
        opacity: 1 !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Animation des onglets
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les statistiques
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 30;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 50);
    });
    
    // Gestion des onglets avec animation
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const targetPane = document.querySelector(e.target.getAttribute('data-bs-target'));
            targetPane.style.opacity = '0';
            targetPane.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                targetPane.style.transition = 'all 0.3s ease';
                targetPane.style.opacity = '1';
                targetPane.style.transform = 'translateY(0)';
            }, 50);
        });
    });
});

// Fonction pour imprimer la fiche
function imprimerFiche() {
    window.print();
}
</script>
{% endblock %}
